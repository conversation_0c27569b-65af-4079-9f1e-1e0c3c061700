/*==============================================================*/
/* DBMS name:      MySQL 5.0                                    */
/* Created on:     2025/5/8 9:55:36                             */
/*==============================================================*/


alter table User 
   drop foreign key FK_USER_RELATIONS_DEPARTME;

drop table if exists Department;


alter table User 
   drop foreign key FK_USER_RELATIONS_DEPARTME;

drop table if exists User;

/*==============================================================*/
/* Table: Department                                            */
/*==============================================================*/
create table Department
(
   departmentId         int not null  comment '',
   departmentName       varchar(20)  comment '',
   primary key (departmentId)
);

/*==============================================================*/
/* Table: User                                                  */
/*==============================================================*/
create table User
(
   userId               int not null  comment '',
   departmentId         int  comment '',
   userName             varchar(20)  comment '',
   primary key (userId)
);

alter table User add constraint FK_USER_RELATIONS_DEPARTME foreign key (departmentId)
      references Department (departmentId) on delete restrict on update restrict;

