package com.hnguigu.spring.test;

import com.hnguigu.spring.config.MainConfig1;
import com.hnguigu.spring.entity.Employee;
import org.junit.Test;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

/*@ContextConfiguration(classes = MainConfig1.class)
@RunWith(SpringJUnit4ClassRunner.class)*/
public class LazyTest {

//    @Autowired
//    private Employee employee;


    @Test
    public void test() {
        AnnotationConfigApplicationContext context =
                new AnnotationConfigApplicationContext(MainConfig1.class);
        Employee employee = context.getBean(Employee.class);
        System.out.println(employee);
    }

    @Test
    public void testScope() {
        AnnotationConfigApplicationContext context =
                new AnnotationConfigApplicationContext(MainConfig1.class);
        Employee employee1 = context.getBean(Employee.class);
        Employee employee2 = context.getBean(Employee.class);

        // true or false
        System.out.println(employee1 == employee2);
    }
}
