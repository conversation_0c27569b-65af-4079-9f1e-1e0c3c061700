package com.hnguigu.spring.test;


import com.hnguigu.spring.config.MainConfig;
import com.hnguigu.spring.config.MainConfig1;
import com.hnguigu.spring.entity.A;
import com.hnguigu.spring.entity.CollectionDemo;
import com.hnguigu.spring.entity.Department;
import com.hnguigu.spring.entity.User;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import java.util.Map;

public class IocTest {

    private ApplicationContext context;
    private User user;

    private A a;

    private CollectionDemo collectionDemo;

    private Department department;

    @Before
    public void setUp() {
        // 创建容器
//        this.context = new ClassPathXmlApplicationContext("applicationContext.xml");

        this.context = new AnnotationConfigApplicationContext(MainConfig.class, MainConfig1.class);

//        this.user = (User) this.context.getBean("user");

        this.a = (A) this.context.getBean("a");
        this.collectionDemo = (CollectionDemo) this.context.getBean("collectionDemo");

        this.department = this.context.getBean(Department.class);

    }

    @Test
    public void test1() {
        // 获取容器中所有的Bean的BeanName
        String[] beanDefinitionNames = this.context.getBeanDefinitionNames();
        for (String beanDefinitionName : beanDefinitionNames) {
            System.out.println(beanDefinitionName);
        }

//        this.a.service();

        // Stream API
        /*String[] arrayValues = this.collectionDemo.getArrayValues();
        for (String arrayValue : arrayValues) {
            System.out.println(arrayValue);
        }*/

        this.collectionDemo.getListValues().stream().forEach(System.out::println);

        Map<String, String> mapValues = this.collectionDemo.getMapValues();
        // map的遍历

        // 获取所有的键 key
        /*Set<String> keySet = mapValues.keySet();
        Iterator<String> iterator = keySet.iterator();

        while (iterator.hasNext()) {
            String key = iterator.next();
            String value = mapValues.get(key);
            System.out.println(key + " = " + value);
        }*/

        // 条目
        /*Set<Map.Entry<String, String>> entries = mapValues.entrySet();
        for (Iterator<Map.Entry<String, String>> iterator = entries.iterator(); iterator.hasNext
        (); ) {
            Map.Entry<String, String> entry = iterator.next();
            String key = entry.getKey();
            String value = entry.getValue();
            System.out.println(key + "---------" + value);

        }*/


    }

    @Test
    public void test2() {
        String[] beanDefinitionNames = this.context.getBeanDefinitionNames();
        for (String beanDefinitionName : beanDefinitionNames) {
            System.out.println(beanDefinitionName);
        }

        System.out.println(this.department);
    }


}
