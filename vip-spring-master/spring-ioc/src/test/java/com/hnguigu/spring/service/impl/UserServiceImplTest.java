package com.hnguigu.spring.service.impl;

import com.hnguigu.spring.config.MainConfig;
import com.hnguigu.spring.config.MainConfig1;
import com.hnguigu.spring.entity.User;
import com.hnguigu.spring.service.UserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

@ContextConfiguration(classes = MainConfig1.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class UserServiceImplTest {

    @Autowired
    private UserService userService;

    @Test
    public void testAddUser() {
        this.userService.addUser(new User(1L, "zhangsan"));
    }
}
