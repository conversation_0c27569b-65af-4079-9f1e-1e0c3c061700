package com.hnguigu.spring.config;

import com.hnguigu.spring.entity.Role;
import com.hnguigu.spring.entity.User;
import com.hnguigu.spring.registry.MyBeanDefinitionRegistry;
import com.hnguigu.spring.selector.MyImportSelector;
import org.springframework.context.annotation.*;

// 表示该类是配置类（相当于applicationContext.xml）
@Configuration


@Primary
@Scope
// @Import 将某个类的类型的Bean纳入Spring容器中
@Import(value = {MyImportSelector.class, User.class, MyBeanDefinitionRegistry.class})
@ComponentScan(basePackages = "com.hnguigu.spring")
public class MainConfig {

    @Bean
    public Role role() {
        Role role = new Role();
        role.setId(1L);
        role.setName("admin");
        return role;
    }
}
