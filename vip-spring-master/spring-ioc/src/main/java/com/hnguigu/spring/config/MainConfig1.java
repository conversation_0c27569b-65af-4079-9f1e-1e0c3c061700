package com.hnguigu.spring.config;

import com.hnguigu.spring.entity.Employee;
import org.springframework.context.annotation.*;

@Configuration
@ComponentScan(basePackages = "com.hnguigu.spring")
public class MainConfig1 {

    @Bean

    // false-----> Employee Bean创建不会延迟。意味着这个Bean在创建容器时就创建
    // true-----> Employee Bean创建会延迟。意味着这个Bean在getBean()方法调用的时候才会创建
    @Lazy(value = true)

    // 该Bean是一个单例Bean
    @Scope(value = "prototype")
    public Employee employee() {
        return new Employee();
    }
}
