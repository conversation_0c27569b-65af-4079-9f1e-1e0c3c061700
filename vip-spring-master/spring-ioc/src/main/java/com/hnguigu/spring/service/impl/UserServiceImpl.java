package com.hnguigu.spring.service.impl;

import com.hnguigu.spring.dao.UserDao;
import com.hnguigu.spring.entity.User;
import com.hnguigu.spring.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.inject.Inject;

@Service
public class UserServiceImpl implements UserService {

    // 按照类型匹配，如果没有匹配到，则会按照名称匹配
    /*@Autowired
    @Qualifier(value = "userDaoMyBatisImpl")*/

    // 先按照名称匹配，如果没有匹配到则按照类型匹配
//    @Resource(name = "userDaoJdbcImpl")

    // 按照类型匹配，如果没有匹配到，则会按照名称匹配
    @Inject
    private UserDao userDao;

    @Override
    public void addUser(User user) {
        if (ObjectUtils.isEmpty(user)) {
            throw new IllegalArgumentException("user is null");
        }

        this.userDao.save(user);
    }
}
