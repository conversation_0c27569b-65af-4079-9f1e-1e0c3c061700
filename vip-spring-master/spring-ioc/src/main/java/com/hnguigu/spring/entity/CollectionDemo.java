package com.hnguigu.spring.entity;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class CollectionDemo {

    private String[] arrayValues;

    private List<String> listValues;

    private Set<String> setValues;

    private Map<String,String> mapValues;

    private Map<String,Person> personMap;


    public String[] getArrayValues() {
        return arrayValues;
    }

    public void setArrayValues(String[] arrayValues) {
        this.arrayValues = arrayValues;
    }

    public List<String> getListValues() {
        return listValues;
    }

    public void setListValues(List<String> listValues) {
        this.listValues = listValues;
    }

    public Set<String> getSetValues() {
        return setValues;
    }

    public void setSetValues(Set<String> setValues) {
        this.setValues = setValues;
    }

    public Map<String, String> getMapValues() {
        return mapValues;
    }

    public void setMapValues(Map<String, String> mapValues) {
        this.mapValues = mapValues;
    }

    public Map<String, Person> getPersonMap() {
        return personMap;
    }

    public void setPersonMap(Map<String, Person> personMap) {
        this.personMap = personMap;
    }
}
