package com.hnguigu.spring.selector;

import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.type.AnnotationMetadata;

/**
 * 自定义导入选择器
 * 作用：将某个Bean纳入Spring容器中
 */
public class MyImportSelector implements ImportSelector {

    @Override
    public String[] selectImports(AnnotationMetadata importingClassMetadata) {
        // 可以在此处编写逻辑代码从而控制Bean是否导入容器
        // AnnotationMetadata参数来进行控制
        return new String[]{"com.hnguigu.spring.entity.Privilege"};
    }
}
