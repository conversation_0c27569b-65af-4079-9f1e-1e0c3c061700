<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context https://www.springframework.org/schema/context/spring-context.xsd">

    <!--
        组件扫描，指定基本包
        扫描 @Controller @Service @Repository @Component
    -->
    <context:component-scan base-package="com.hnguigu.spring"/>

    <bean id="user" class="com.hnguigu.spring.entity.User">
        <property name="id" value="10000"/>
        <property name="name" value="zhangsan"/>
    </bean>

    <bean id="a" class="com.hnguigu.spring.entity.A">
        <!--        <property name="b" ref="b"/>-->

        <constructor-arg name="b" ref="b"/>
    </bean>

    <bean id="b" class="com.hnguigu.spring.entity.B"/>

    <bean id="collectionDemo" class="com.hnguigu.spring.entity.CollectionDemo">
        <property name="arrayValues">
            <array>
                <value>zhangsan</value>
                <value>lisi</value>
                <value>wangwu</value>
            </array>
        </property>

        <property name="listValues">
            <list>
                <value>aaa</value>
                <value>bbb</value>
                <value>ccc</value>
            </list>
        </property>

        <property name="setValues">
            <set>
                <value>aaa</value>
                <value>bbb</value>
                <value>ccc</value>
            </set>
        </property>

        <property name="mapValues">
            <map>
                <entry key="key1">
                    <value>helloworld</value>
                </entry>

                <entry key="key2">
                    <value>fdsfsdfds</value>
                </entry>

                <entry key="key3">
                    <value>gfggggg</value>
                </entry>
            </map>
        </property>

        <property name="personMap">
            <map>
                <entry key="key1">
                    <ref bean="person1"/>
                </entry>

                <entry key="key2">
                    <ref bean="person2"/>
                </entry>

                <entry key="key3">
                    <ref bean="person3"/>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="person1" class="com.hnguigu.spring.entity.Person">
        <property name="id" value="10000"/>
        <property name="name" value="zhangsan"/>
    </bean>
    <bean id="person2" class="com.hnguigu.spring.entity.Person">
        <property name="id" value="10001"/>
        <property name="name" value="lisi"/>
    </bean>
    <bean id="person3" class="com.hnguigu.spring.entity.Person">
        <property name="id" value="10002"/>
        <property name="name" value="wangwu"/>
    </bean>

</beans>
