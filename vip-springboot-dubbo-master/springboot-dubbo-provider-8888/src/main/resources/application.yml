server:
  port: 8888

spring:
  application:
    name: springboot-dubbo-provider-8888

#dubbo:
#  application:
#    name: ${spring.application.name}
#  registry:
#    #    p1:
#    #      address: zookeeper://**************:2181
#    #      timeout: 20000
#    #    p2:
#    #      address: zookeeper://**************:2181
#    #      timeout: 20000
#    address: zookeeper://**************:2181
#    timeout: 20000
#  protocol:
#    #    p1:
#    #      name: dubbo
#    #      port: 20881
#    #    p2:
#    #      name: hessian
#    #      port:
#    name: dubbo
#    port: 20881
#  scan:
#    base-packages: com.hnguigu.springboot.service




dubbo:
  application:
    name: ${spring.application.name}
  registry:
    address: zookeeper://**************:2181
    timeout: 20000
  protocol:
    name: dubbo
    port: 20881
  scan:
    base-packages: com.hnguigu.springboot.service

















