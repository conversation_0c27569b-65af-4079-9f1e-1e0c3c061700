package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.Goods;
import com.hnguigu.springboot.service.GoodsService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.ObjectUtils;

import java.util.concurrent.TimeUnit;

// 服务提供者配置了超时时间，超时时间为5秒
@DubboService(timeout = 5000)
public class GoodsServiceImpl implements GoodsService {

    @Override
    public Goods findGoodsById(Long id) {
        if (ObjectUtils.isEmpty(id) || id <= 0) {
            throw new IllegalArgumentException("id必须大于0");
        }

        Goods goods = new Goods();
        goods.setId(id);
        goods.setName("商品" + id);
        goods.setPrice(200D);

        try {
            // 线程休眠5秒
            TimeUnit.SECONDS.sleep(4);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        return goods;
    }
}
