package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.entity.Order;
import com.hnguigu.springboot.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @PostMapping("/{goodsId}")
    public String createOrder(@RequestBody Order order, @PathVariable Long goodsId) {
        this.orderService.createOrder(order, goodsId);
        return "success";
    }
}
