package com.hnguigu.springboot.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class R<T> implements Serializable {

    private static final long serialVersionUID = 2951769876055172321L;

    /**
     * 执行的状态
     */
    private Boolean flag;

    /**
     *  返回到前端的消息
     */
    private String message;

    /**
     * 40000  xxxxxx
     * 40004  xxxxx
     * 50000  xxxxxx
     * 20000 success
     * 返回到前端的状态码
     */
    private String statusCode;

    /**
     * 返回到前端的数据
     */
    private T data;

    /**
     *
     * @param data
     * @return
     * @param <T>
     */
    public static <T> R<T> success(T data) {
        R<T> r = new R<T>();
        r.setFlag(true);
        r.setMessage("success");
        r.setStatusCode("20000");
        r.setData(data);
        return r;
    }

    public static <T> R<T> error(String message) {
        R<T> r = new R<T>();
        r.setFlag(false);
        r.setMessage(message);
        r.setStatusCode("50000");
        return r;
    }
}
