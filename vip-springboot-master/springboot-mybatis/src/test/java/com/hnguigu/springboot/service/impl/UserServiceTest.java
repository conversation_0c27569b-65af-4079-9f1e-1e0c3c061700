package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class UserServiceTest {

    @Autowired
    private UserService userService;

    @Test
    void testFindUserListByDepartmentName() {
        this.userService.findUserListByDepartmentName("IBM")
                .stream()
                .forEach(System.out::println);
    }
}
