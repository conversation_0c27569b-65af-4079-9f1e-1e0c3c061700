package com.hnguigu.springboot.service.impl;

import com.github.pagehelper.PageInfo;
import com.hnguigu.springboot.dto.DepartmentQuery;
import com.hnguigu.springboot.entity.Department;
import com.hnguigu.springboot.service.DepartmentService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class DepartmentServiceImplTest {

    @Autowired
    private DepartmentService departmentService;

    @Test
    void testDeleteDepartment() {
        Department department = this.departmentService.findDepartmentById(2L);
        this.departmentService.deleteDepartment(department);
    }

    @Test
    void modifyDepartment() {
        Department department = this.departmentService.findDepartmentById(3L);
        department.setName("Mac");
        this.departmentService.modifyDepartment(department);
    }

    @Test
    void findDepartmentById() {
    }

    @Test
    void findAll() {
        this.departmentService.findAll().stream().forEach(System.out::println);
    }

    @Test
    void findByCondition() {
        DepartmentQuery departmentQuery = new DepartmentQuery();
        departmentQuery.setName("a");
        this.departmentService.findByCondition(departmentQuery).stream().forEach(System.out::println);
    }

    @Test
    void findPage() {
        PageInfo<Department> pageInfo = this.departmentService.findPage(3, 2);
        System.out.println(pageInfo);
    }

    @Test
    void testFindByUserName() {
        Department department = this.departmentService.findDepartmentByUserName("zhangsan");
        System.out.println(department);
    }
}
