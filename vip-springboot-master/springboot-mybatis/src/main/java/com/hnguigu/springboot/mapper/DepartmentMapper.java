package com.hnguigu.springboot.mapper;

import com.github.pagehelper.PageInfo;
import com.hnguigu.springboot.dto.DepartmentQuery;
import com.hnguigu.springboot.entity.Department;

import java.util.List;

public interface DepartmentMapper {

    /**
     * 保存部门对象
     *
     * @param department
     */
    void save(Department department);

    void delete(Department department);

    void update(Department department);

    Department findById(Long id);

    List<Department> findAll();

    List<Department> find(DepartmentQuery departmentQuery);

    Department findByUserName(String name);

//    PageInfo<Department> findByPage(int pageNum, int pageSize);
//
//    PageInfo<Department> findByPage(int pageNum, int pageSize, DepartmentQuery departmentQuery);
}
