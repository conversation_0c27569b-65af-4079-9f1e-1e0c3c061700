package com.hnguigu.springboot.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnguigu.springboot.dto.DepartmentDTO;
import com.hnguigu.springboot.dto.DepartmentQuery;
import com.hnguigu.springboot.entity.Department;
import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.exception.NotAllowDeleteException;
import com.hnguigu.springboot.mapper.DepartmentMapper;
import com.hnguigu.springboot.mapper.UserMapper;
import com.hnguigu.springboot.service.DepartmentService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
@Transactional
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public void addDepartment(DepartmentDTO departmentDto) {
        if (ObjectUtils.isEmpty(departmentDto)) {
            throw new IllegalArgumentException("departmentDto不能为空");
        }

        Department department = new Department();
        BeanUtils.copyProperties(departmentDto, department);

        this.departmentMapper.save(department);
    }

    @Override
    public void deleteDepartment(Department department) {
        if (ObjectUtils.isEmpty(department)) {
            throw new IllegalArgumentException("departmentDto不能为空");
        }

        // TODO 查询该部门有没有用户，如果没有用户则直接删除，如果有不能删除
        List<User> userList = this.userMapper.findUsersByDepartmentId(department.getId());
        if (!CollectionUtils.isEmpty(userList)) {
            throw new NotAllowDeleteException("该部门有用户,不能删除!");
        }

        this.departmentMapper.delete(department);
    }

    @Override
    public void modifyDepartment(Department department) {
        if (ObjectUtils.isEmpty(department)) {
            throw new IllegalArgumentException("departmentDto不能为空");
        }

        this.departmentMapper.update(department);
    }

    @Override
    public Department findDepartmentById(Long id) {
        if (ObjectUtils.isEmpty(id) || id <= 0) {
            throw new IllegalArgumentException("id不能为空，且不能为零或负数");
        }

        return this.departmentMapper.findById(id);
    }

    @Override
    public List<Department> findAll() {
        return this.departmentMapper.findAll();
    }

    @Override
    public List<Department> findByCondition(DepartmentQuery departmentQuery) {
        if (ObjectUtils.isEmpty(departmentQuery)) {
            return this.departmentMapper.findAll();
        }

        return this.departmentMapper.find(departmentQuery);
    }

    @Override
    public PageInfo<Department> findPage(Integer pageNum, Integer pageSize) {
        PageInfo<Department> pageInfo = null;

        if (ObjectUtils.isEmpty(pageNum) || ObjectUtils.isEmpty(pageSize)) {
            throw new IllegalArgumentException("pageNum和pageSize不能为空");
        }

        PageHelper.startPage(pageNum, pageSize);
        List<Department> departmentList = this.departmentMapper.findAll();

        pageInfo = new PageInfo<>(departmentList);

        return pageInfo;
    }

    @Override
    public Department findDepartmentByUserName(String name) {
        if (StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("用户name不能为空");
        }

        return this.departmentMapper.findByUserName(name);
    }
}
