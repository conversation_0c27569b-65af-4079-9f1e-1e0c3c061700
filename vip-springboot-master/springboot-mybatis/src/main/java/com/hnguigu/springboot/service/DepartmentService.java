package com.hnguigu.springboot.service;

import com.github.pagehelper.PageInfo;
import com.hnguigu.springboot.dto.DepartmentDTO;
import com.hnguigu.springboot.dto.DepartmentQuery;
import com.hnguigu.springboot.entity.Department;

import java.util.List;

public interface DepartmentService {

    void addDepartment(DepartmentDTO department);

    void deleteDepartment(Department department);

    void modifyDepartment(Department department);

    Department findDepartmentById(Long id);

    List<Department> findAll();

    List<Department> findByCondition(DepartmentQuery departmentQuery);

    PageInfo<Department> findPage(Integer pageNum, Integer pageSize);

    /**
     * 用户名称查询所在部门
     */
    Department findDepartmentByUserName(String name);
}
