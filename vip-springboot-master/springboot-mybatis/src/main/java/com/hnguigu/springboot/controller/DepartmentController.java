package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.dto.DepartmentDTO;
import com.hnguigu.springboot.service.DepartmentService;
import com.hnguigu.springboot.vo.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/departments")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    @PostMapping
    public R add(@RequestBody DepartmentDTO departmentDTO) {
        this.departmentService.addDepartment(departmentDTO);
        return R.success(null);
    }
}
