package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.mapper.UserMapper;
import com.hnguigu.springboot.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

@Service
@Transactional
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public List<User> findUserListByDepartmentName(String name) {
        if (StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("部门name不能为空");
        }

        return this.userMapper.findUsersByDepartmentName(name);
    }
}
