package com.hnguigu.springboot.exception;

/**
 * 自定义异常类
 * RuntimeException (Uncheck exception)
 * Exception (Checked Exception) try...catch
 */
public class NotAllowDeleteException extends RuntimeException {

    // 异常消息
    private String message;

    public NotAllowDeleteException() {
    }

    public NotAllowDeleteException(String message) {
        super(message);
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
