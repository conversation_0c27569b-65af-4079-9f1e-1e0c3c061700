package com.hnguigu.springboot.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class Department implements Serializable {

    private static final long serialVersionUID = 6082734003889496314L;
    private Long id;
    private String name;
    private String location;
}
