<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hnguigu.springboot.mapper.DepartmentMapper">

    <resultMap id="DepartmentResultMap" type="Department">
        <id column="department_id" property="id"/>
        <result property="name" column="department_name"/>
        <result property="location" column="department_location"/>
    </resultMap>

    <sql id="DepartmentSql">
        DEPARTMENT_ID,
        DEPARTMENT_NAME,
        DEPARTMENT_LOCATION
    </sql>

    <insert id="save">
        INSERT INTO MYBATIS_DEPARTMENT (DEPARTMENT_NAME, DEPARTMENT_LOCATION)
        VALUES (#{name}, #{location})
    </insert>

    <update id="update">
        UPDATE MYBATIS_DEPARTMENT
        <set>
            <if test="name != null">
                DEPARTMENT_NAME = #{name},
            </if>
            <if test="location != null">
                DEPARTMENT_LOCATION = #{location}
            </if>
        </set>
        WHERE DEPARTMENT_ID = #{id}
    </update>

    <delete id="delete">
        DELETE
        FROM MYBATIS_DEPARTMENT
        WHERE DEPARTMENT_ID = #{id}
    </delete>


    <select id="findById" resultMap="DepartmentResultMap">
        SELECT
        <include refid="DepartmentSql"/>
        FROM MYBATIS_DEPARTMENT
        WHERE DEPARTMENT_ID = #{id}
    </select>

    <select id="findAll" resultMap="DepartmentResultMap">
        SELECT
        <include refid="DepartmentSql"/>
        FROM MYBATIS_DEPARTMENT
    </select>

    <select id="find" resultMap="DepartmentResultMap">
        SELECT
        <include refid="DepartmentSql"/>
        FROM MYBATIS_DEPARTMENT
        <where>
            <if test="name != null and name != ''">
                DEPARTMENT_NAME LIKE '%${name}%'
            </if>

            <if test="location != null and location != ''">
                DEPARTMENT_LOCATION LIKE '%${location}%'
            </if>
        </where>
    </select>

    <select id="findByUserName" resultMap="DepartmentResultMap">
        SELECT MD.DEPARTMENT_ID,
               MD.DEPARTMENT_NAME,
               MD.DEPARTMENT_LOCATION
        FROM MYBATIS_DEPARTMENT MD
                 LEFT JOIN MYBATIS_USER MU ON MD.DEPARTMENT_ID = MU.DEPARTMENT_ID
        WHERE MU.USER_NAME = #{name}
    </select>
</mapper>
