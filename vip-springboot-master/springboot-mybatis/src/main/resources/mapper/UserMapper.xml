<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hnguigu.springboot.mapper.UserMapper">

    <resultMap id="UserResultMap" type="User">
        <id property="id" column="user_id"/>
        <result property="name" column="user_name"/>
        <result property="password" column="user_password"/>
        <result property="salary" column="user_salary"/>
        <result property="birthday" column="user_birthday"/>
        <result property="departmentId" column="department_id"/>
    </resultMap>

    <select id="findUsersByDepartmentId" resultMap="UserResultMap">
        SELECT USER_ID,
               USER_NAME,
               USER_PASSWORD,
               USER_SALARY,
               USER_BIRTHDAY,
               DEPARTMENT_ID
        FROM MYBATIS_USER
        WHERE DEPARTMENT_ID = #{id}
    </select>

    <select id="findUsersByDepartmentName" resultMap="UserResultMap">
        SELECT MU.USER_ID,
               MU.USER_NAME,
               MU.USER_PASSWORD,
               MU.USER_SALARY,
               MU.USER_BIRTHDAY,
               MU.DEPARTMENT_ID
        FROM MYBATIS_USER MU
                 LEFT JOIN MYBATIS_DEPARTMENT MD ON MU.DEPARTMENT_ID = MD.DEPARTMENT_ID
        WHERE MD.DEPARTMENT_NAME = #{name}
    </select>
</mapper>
