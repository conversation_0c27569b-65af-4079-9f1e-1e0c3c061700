server:
  port: 8888

spring:
  application:
    name: springboot-mybatis
  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: *****************************************************************************************************
#    username: root
#    password: root
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      test-on-borrow: true
      test-on-return: true
      test-while-idle: true
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *****************************************************************************************************
      username: root
      password: root

mybatis:
  type-aliases-package: com.hnguigu.springboot.entity   #自动将这个包下的类的简单名作为别名
  configuration:
    lazy-loading-enabled: true        #懒加载总开关
    aggressive-lazy-loading: false    #侵入式延迟一般设置false
    cache-enabled: true
  mapper-locations: classpath:mapper/*Mapper.xml



