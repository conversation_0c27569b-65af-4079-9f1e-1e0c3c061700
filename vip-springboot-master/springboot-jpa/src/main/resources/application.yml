server:
  port: 8888

spring:
  application:
    name: springboot-jpa

  datasource:
    url: **************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    generate-ddl: true    #生成DDL（数据定义语言）自动创建表
    show-sql: true       #显示sql语句 便于调试
    open-in-view: true   #开启视图  JPA的有一种查询（getRefenrence()），查询的代理对象(proxy)，代理对象必须要进行初始化。初始化必须要存在Session。所以Session不能过早地关闭



