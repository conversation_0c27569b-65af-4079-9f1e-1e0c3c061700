package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.Person;
import com.hnguigu.springboot.repository.PersonRepository;
import com.hnguigu.springboot.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class PersonServiceImpl implements PersonService {

    @Autowired
    private PersonRepository personRepository;

    @Override
    public void addPerson(Person person) {
        this.personRepository.save(person);
    }
}
