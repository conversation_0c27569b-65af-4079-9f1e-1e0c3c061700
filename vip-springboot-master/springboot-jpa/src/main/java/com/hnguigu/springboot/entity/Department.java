package com.hnguigu.springboot.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "jpa_department")
public class Department implements Serializable {

    private static final long serialVersionUID = 6082734003889496314L;

    // 标识属性
    @Id
    // 主键生成策略
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "department_id", length = 10)
    private Integer id;

    @Column(name = "department_name", length = 20, nullable = false)
    private String name;

    @Column(name = "department_location", length = 20, nullable = false)
    private String location;

    // 1对多关系，部门可以有多个用户
    @OneToMany(mappedBy = "department")
    private Set<User> users;

    @Override
    public String toString() {
        return "Department{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", location='" + location + '\'' +
                '}';
    }
}
