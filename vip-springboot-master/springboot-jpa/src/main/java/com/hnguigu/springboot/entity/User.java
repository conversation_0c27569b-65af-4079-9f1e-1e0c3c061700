package com.hnguigu.springboot.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "jpa_user")
public class User implements Serializable {

    private static final long serialVersionUID = 6550995370786726671L;

    @Id
    // 主键生成策略
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "user_id",length = 10)
    private Integer id;

    @Column(name = "user_name",length = 20,nullable = false)
    private String name;

    @Column(name = "user_password",length = 20,nullable = false)
    private String password;

    @Column(name = "user_salary",length = 6,precision = 2, nullable = false)
    private Float salary;

    @Column(name = "user_birthday",nullable = false)

    // 指定年月日 or 时分秒 or 年月日时分秒
    @Temporal(TemporalType.DATE)
    private Date birthday;

    // 多对一
    @ManyToOne

    // 生成外键，JoinColumn（连接字段）
    @JoinColumn(name = "department_id")
    private Department department;

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", password='" + password + '\'' +
                ", salary=" + salary +
                ", birthday=" + birthday +
                '}';
    }
}
