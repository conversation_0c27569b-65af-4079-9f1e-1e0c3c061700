package com.hnguigu.springboot.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 身份证
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "jpa_id_card")
public class IdCard implements Serializable {

    private static final long serialVersionUID = -1784352729709311154L;

    @Id
    // 主键生成策略
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_card_id", length = 10)
    private Integer id;

    @Column(name = "card_number", length = 20, nullable = false)
    private String cardNumber;

    @OneToOne(mappedBy = "idCard")
    private Person person;
}
