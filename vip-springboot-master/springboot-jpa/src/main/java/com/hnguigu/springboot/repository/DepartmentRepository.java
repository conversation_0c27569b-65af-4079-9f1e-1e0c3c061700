package com.hnguigu.springboot.repository;

import com.hnguigu.springboot.entity.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface DepartmentRepository extends JpaRepository<Department, Integer> {

    /**
     * 该方法有规范：方法的名称开头必须是find get search
     *
     * @param name
     * @return
     */
    List<Department> findByNameLike(String name);

    List<Department> findByNameAndLocation(String name, String location);

    @Query("select d FROM Department d left join d.users u where u.name = ?1")
    Department findByUserName(String name);
}
