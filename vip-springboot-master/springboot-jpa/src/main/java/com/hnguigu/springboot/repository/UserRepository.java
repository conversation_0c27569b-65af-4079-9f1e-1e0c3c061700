package com.hnguigu.springboot.repository;

import com.hnguigu.springboot.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface UserRepository extends JpaRepository<User, Integer> {

    List<User> findBySalaryBetween(Float beginSalary, Float endSalary);

    /**
     * JPQL （JPA Query Language）
     * 这种查询语言纯面向对象的查询语言。与SQL非常相似
     *
     * @param departmentName
     * @return
     */
    @Query(value = "select u FROM User u left join u.department d where d.name = ?1")
    List<User> findByDepartmentName(String departmentName);
}
