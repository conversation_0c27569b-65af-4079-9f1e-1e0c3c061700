package com.hnguigu.springboot.entity;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "jpa_teacher")
public class Teacher implements Serializable {

    private static final long serialVersionUID = -6552648607973962936L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "teacher_id", length = 10)
    private Integer id;

    @Column(name = "teacher_name", length = 20, nullable = false)
    private String name;

    @Column(name = "teacher_salary", length = 6, nullable = false)
    private Float salary;

    @ManyToMany
    @JoinTable(name = "jpa_teacher_student",
            joinColumns = {@JoinColumn(name = "teacher_id")},
            inverseJoinColumns = {@JoinColumn(name = "student_id")})
    private Set<Student> students = new HashSet<>();
}
