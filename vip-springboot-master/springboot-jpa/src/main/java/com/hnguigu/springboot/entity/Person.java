package com.hnguigu.springboot.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "jpa_person")
public class Person implements Serializable {

    private static final long serialVersionUID = 954483839960695342L;

    @Id
    // 主键生成策略
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "person_id", length = 10)
    private Integer id;

    @Column(name = "person_name", length = 20, nullable = false)
    private String name;

    @Column(name = "person_password", length = 20, nullable = false)
    private String password;

    @Column(name = "person_salary", length = 6, precision = 2, nullable = false)
    private Float salary;

    @Column(name = "person_birthday", nullable = false)

    // 指定年月日 or 时分秒 or 年月日时分秒
    @Temporal(TemporalType.DATE)
    private Date birthday;

    @OneToOne

    // 此处必须写上unique=true
    @JoinColumn(name = "id_card_id", unique = true)
    private IdCard idCard;
}
