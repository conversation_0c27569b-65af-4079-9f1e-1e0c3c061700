package com.hnguigu.springboot.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "jpa_student")
public class Student implements Serializable {

    private static final long serialVersionUID = -6552648607973962936L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "student_id", length = 10)
    private Integer id;

    @Column(name = "student_name",length = 20,nullable = false)
    private String name;

    @Column(name = "student_score",length = 6,nullable = false)
    private Float score;

    @ManyToMany
    @JoinTable(name = "jpa_teacher_student",
            joinColumns = {@JoinColumn(name = "student_id")},
            inverseJoinColumns = {@JoinColumn(name = "teacher_id")})
    private Set<Teacher> teachers = new HashSet<>();

}
