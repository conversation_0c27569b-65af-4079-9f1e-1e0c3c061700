package com.hnguigu.springboot.service;

import com.hnguigu.springboot.entity.Department;

import java.util.List;

public interface DepartmentService {

    void addDepartment(Department department);

    void deleteDepartment(Department department);

    void modifyDepartment(Department department);

    Department findDepartmentById(Integer id);

    List<Department> findAllDepartmentList();

    /**
     * 根据部门名称查询部门列表
     */
    List<Department> findDepartmentListByName(String name);

    List<Department> findDepartmentListByNameAndLocation(String name, String location);

    Department findDepartmentByUserName(String name);
}
