package com.hnguigu.springboot.service.impl;

import com.fasterxml.jackson.core.TreeCodec;
import com.hnguigu.springboot.entity.Department;
import com.hnguigu.springboot.repository.DepartmentRepository;
import com.hnguigu.springboot.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;

@Service
@Transactional
public class DepartmentServiceImpl implements DepartmentService {

    @Autowired
    private DepartmentRepository departmentRepository;
    @Autowired
    private TreeCodec treeCodec;

    @Override
    public void addDepartment(Department department) {
        if (department == null) {
            throw new IllegalArgumentException("Department cannot be null");
        }

        this.departmentRepository.saveAndFlush(department);
    }

    @Override
    public void deleteDepartment(Department department) {

    }

    @Override
    public void modifyDepartment(Department department) {

    }

    @Override
    public Department findDepartmentById(Integer id) {
        if (ObjectUtils.isEmpty(id) || id <= 0) {
            throw new IllegalArgumentException("Invalid department ID");
        }

        // findById() vs getReferenceById()

//        Department department = this.departmentRepository.findById(id).get();
//        return department;

        Department department = this.departmentRepository.getReferenceById(id);
        return department;

    }

    @Override
    public List<Department> findAllDepartmentList() {
        return this.departmentRepository.findAll();
    }

    @Override
    public List<Department> findDepartmentListByName(String name) {
        if (ObjectUtils.isEmpty(name)) {
            throw new IllegalArgumentException("Invalid department name");
        }

        // QBE(Query By Example Hibernate框架提出的) 查询
//        this.departmentRepository.findAll()

        return this.departmentRepository.findByNameLike(name);
    }

    @Override
    public List<Department> findDepartmentListByNameAndLocation(String name, String location) {
        if (ObjectUtils.isEmpty(name)) {
            throw new IllegalArgumentException("Invalid department name");
        }

        // QBE(Query By Example Hibernate框架提出的) 查询
//        this.departmentRepository.findAll()

        return this.departmentRepository.findByNameAndLocation(name,location);
    }

    @Override
    public Department findDepartmentByUserName(String name) {
        if (ObjectUtils.isEmpty(name)) {
            throw new IllegalArgumentException("Invalid user name");
        }

        return this.departmentRepository.findByUserName(name);
    }
}
