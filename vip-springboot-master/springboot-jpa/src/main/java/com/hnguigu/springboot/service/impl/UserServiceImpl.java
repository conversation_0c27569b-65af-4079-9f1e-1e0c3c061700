package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.repository.UserRepository;
import com.hnguigu.springboot.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;

@Service
@Transactional
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Override
    public List<User> findUserListBySalaryRange(Float beginSalary, Float endSalary) {
        if (ObjectUtils.isEmpty(beginSalary) || ObjectUtils.isEmpty(endSalary)) {
            throw new IllegalArgumentException("beginSalary和endSalary不能为空");
        }

        if (beginSalary > endSalary) {
            throw new IllegalArgumentException("beginSalary不能大于endSalary");
        }


        return this.userRepository.findBySalaryBetween(beginSalary, endSalary);
    }

    @Override
    public List<User> findUserListByDepartmentName(String name) {
        if (ObjectUtils.isEmpty(name)) {
            throw new IllegalArgumentException("部门name不能为空");
        }

        return this.userRepository.findByDepartmentName(name);
    }


















}
