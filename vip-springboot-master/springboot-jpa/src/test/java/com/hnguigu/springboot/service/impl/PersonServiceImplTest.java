package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.Person;
import com.hnguigu.springboot.service.PersonService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class PersonServiceImplTest {

    @Autowired
    private PersonService personService;

    @Test
    void addPerson() {
        Person person = new Person();
        person.setName("zhangsan");
        person.setPassword("admin");
        person.setSalary(11.11F);
        person.setBirthday(new java.util.Date());
        personService.addPerson(person);
    }
}
