package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class UserServiceImplTest {

    @Autowired
    private UserService userService;

    @Test
    void findUserListBySalaryRange() {
        List<User> userList = this.userService.findUserListBySalaryRange(20.00F, 80.00F);
        userList.forEach(System.out::println);
    }

    @Test
    void findUserByDepartmentName() {
        List<User> userList = this.userService.findUserListByDepartmentName("Apple");
        userList.stream().forEach(System.out::println);
    }
}
