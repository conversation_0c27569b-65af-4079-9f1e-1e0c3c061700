package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.Department;
import com.hnguigu.springboot.service.DepartmentService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class DepartmentServiceImplTest {

    @Autowired
    private DepartmentService departmentService;

    @Test
    void addDepartment() {
        Department department = new Department();
        department.setName("Apple");
        department.setLocation("LOS");
        this.departmentService.addDepartment(department);
    }

    @Test
    void testFindById() {
        Department department = this.departmentService.findDepartmentById(1);
        System.out.println(department);
    }

    @Test
    void testFindAll() {
        this.departmentService.findAllDepartmentList().forEach(System.out::println);
    }

    @Test
    public void testFindByName() {
        List<Department> departmentList = this.departmentService.findDepartmentListByName("%p%");
        departmentList.forEach(System.out::println);
    }

    @Test
    public void testFindDeaprtmentByUserName() {
        Department department = this.departmentService.findDepartmentByUserName("zhangsan");
        System.out.println(department);
    }


}
