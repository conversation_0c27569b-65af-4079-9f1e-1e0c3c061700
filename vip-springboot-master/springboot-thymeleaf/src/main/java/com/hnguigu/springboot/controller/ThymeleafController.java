package com.hnguigu.springboot.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class ThymeleafController {

    @GetMapping("/thymeleaf/index")
    public String index(Model model) {

        model.addAttribute("info", "这是一个Thymeleaf案例");
        model.addAttribute("items", new String[]{"a", "b", "c"});

        return "index";
    }
}
