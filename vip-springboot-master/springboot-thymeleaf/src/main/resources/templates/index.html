<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>这是一个Thymeleaf案例</title>
</head>
<body>
<!--显示文本-->
<p th:text="${info}">helloworld</p>

<div></div>

<!-- 当 user 不为 null 时显示 -->
<div th:if="${user != null}">
    欢迎您, <span th:text="${user.name}">用户</span>
</div>

<!-- 当 user 为 null 时显示 -->
<div th:unless="${user != null}">
    请先登录
</div>

<!-- Thymeleaf loop example -->
<ul>
    <li th:each="item : ${items}" th:text="${item}">Item</li>
</ul>

<!-- thymeleaf 条件判断 -->
<div th:if="${user.age > 18}">
    You are an adult.
</div>
<div th:unless="${user.age > 18}">
    You are not an adult.
</div>


</body>
</html>
