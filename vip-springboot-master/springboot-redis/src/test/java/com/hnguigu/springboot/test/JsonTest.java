package com.hnguigu.springboot.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.hnguigu.springboot.entity.User;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class JsonTest {

    @Test
    public void testParse() {
        /*String userJSONString = "{'id': 1000,'name': 'zhangsan','age': 23}";
        // String------>对象
        // cast 造型 转型 强制类型转换
        Object user = JSON.parse(userJSONString);
        System.out.println(user);*/


        String userJSONArrayString = "[{'id': 1000,'name': 'zhang<PERSON>','age': 23},{'id': 1001," +
                "'name': " +
                "'lisi','age': 33}]";

        JSONArray jsonArray = JSON.parseArray(userJSONArrayString);

        for (int i = 0; i < jsonArray.size(); i++) {
            System.out.println(jsonArray.get(i));
        }



    }

    @Test
    public void testToJSON() {
        User user =new User();
        user.setId(20000);
        user.setName("王二麻子");
        user.setAge(33);

        String jsonString = JSON.toJSONString(user);
        System.out.println(jsonString);
    }
}
