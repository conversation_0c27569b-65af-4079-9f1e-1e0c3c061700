package com.hnguigu.springboot.test;

import com.hnguigu.springboot.entity.User;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.ObjectUtils;

import java.util.*;

@SpringBootTest
public class RedisTest {

    /**
     * 为什么可以直接使用？？？？
     * 自动装配
     * 当导入spring-boot-starter-data-redis依赖时
     * RedisAutoConfiguration会自动装配RedisTemplate
     */
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RedisTemplate myRedisTemplate;

    @Autowired
    private RedisTemplate<String, User> userRedisTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void test1() {
        // 向redis设置了键
        this.redisTemplate.opsForValue().set("name", "zhangsan");

        // 获取键的值
        Object name = this.redisTemplate.opsForValue().get("name");
        System.out.println(name);

        // 问题？写的key出现了乱码，形如：\xac\xed\x00\x05t\x00\x04name
        // 如何解决这个乱码问题？
        // 1. 设置序列化器
        // 2. 使用另外一个模板对象StringRedisTemplate
    }

    @Test
    public void test2() {
        this.stringRedisTemplate.opsForValue().set("password", "admin");
    }

    @Test
    public void test3() {
//        this.myRedisTemplate.opsForValue().set("salary",12.34F);

        Map<String, String> map = new HashMap<>();
        map.put("age", "12");
        map.put("birthday", "1234-12-12");
        map.put("gender", "MALE");
        this.myRedisTemplate.opsForValue().multiSet(map);

        List resultList = this.myRedisTemplate.opsForValue().multiGet(Arrays.asList("age",
                "birthday", "gender"));
        resultList.stream().forEach(System.out::println);
    }

    @Test
    public void test4() {
        // setnx 分布式锁 redision
        this.myRedisTemplate.opsForValue().setIfAbsent("age", "34");
        Object age = this.myRedisTemplate.opsForValue().get("age");
        System.out.println(age);//12
    }

    /**
     * Hash数据类型
     * 购物车
     */
    @Test
    public void testHash() {
        this.redisTemplate.opsForHash().put("cart:10000", "apple", 1);
        this.redisTemplate.opsForHash().put("cart:10000", "huawei", 2);

        Object count = this.redisTemplate.opsForHash().get("cart:10000", "huawei");
        System.out.println(count);

        System.out.println();

        Long size = this.redisTemplate.opsForHash().size("cart:10000");
        System.out.println("一共有" + size + "键值对");

        System.out.println();

        // boundHashOps()
        this.redisTemplate.boundHashOps("cart:10000").delete("apple");
        size = this.redisTemplate.opsForHash().size("cart:10000");
        System.out.println("删除后一共有" + size + "键值对");

        System.out.println();

        Set keySet = this.redisTemplate.opsForHash().keys("cart:10000");
        keySet.stream().forEach(System.out::println);
    }

    @Test
    public void testList() {

        this.redisTemplate.opsForList().leftPush("abc:20000", "apple");
        this.redisTemplate.opsForList().leftPush("abc:20000", "huawei");
        this.redisTemplate.opsForList().leftPush("abc:20000", "vivo");

        Object result = this.redisTemplate.opsForList().rightPop("abc:20000");
        System.out.println(result);

        System.out.println();

        result = this.redisTemplate.opsForList().leftPop("abc:20000");
        System.out.println(result);

        System.out.println("====================================");

        this.redisTemplate.opsForList().range("abc:20000", 0, -1).stream().forEach(System.out::println);
    }

    @Test
    public void testSet() {
        this.redisTemplate.opsForSet().add("abc:30000", "apple", "huawei", "vivo");

        this.redisTemplate.opsForSet().members("abc:30000").stream().forEach(System.out::println);

    }

    @Test
    public void testSet1() {
        this.redisTemplate.opsForSet().add("abc:30000", "apple", "huawei", "vivo");
        this.redisTemplate.opsForSet().add("abc:40000", "google", "oppo", "apple");


        // 交集
        /*this.redisTemplate.opsForSet().intersect("abc:30000", "abc:40000")
                .stream()
                .forEach(System.out::println);*/

        // 并集
        /*this.redisTemplate.opsForSet().union("abc:30000", "abc:40000")
                .stream()
                .forEach(System.out::println);*/

        // 差集
        this.redisTemplate.opsForSet().difference("abc:30000", "abc:40000").stream().forEach(System.out::println);

    }

    @Test
    public void testWriteCache() {
        User user = new User();
        user.setId(10000);
        user.setName("zhangsan");
        user.setAge(23);

        // 写缓存
        this.redisTemplate.opsForValue().set("user:" + user.getId(), user);
    }

    @Test
    public void readCache() {
        // 读缓存
        User u = (User) this.redisTemplate.opsForValue().get("user:10000");
        if (ObjectUtils.isEmpty(u)) {
            // TODO 查数据库
            User user = new User();
            user.setId(10000);
            user.setName("zhangsan");
            user.setAge(23);

            // 值上没有设置序列化器，因此写入缓存的是字节
            this.redisTemplate.opsForValue().set("user:" + user.getId(), user);

        } else {
            System.out.println("执行你的业务。" + u);
        }
    }

    /**
     * 将对象转换成JSON字符串，然后将其保存到缓存中
     */
    @Test
    public void testWriteCacheWithJSON() {
        User user = new User();
        user.setId(10000);
        user.setName("zhangsan");
        user.setAge(23);

        this.userRedisTemplate.opsForValue().set("user:20000", user);
    }
}
