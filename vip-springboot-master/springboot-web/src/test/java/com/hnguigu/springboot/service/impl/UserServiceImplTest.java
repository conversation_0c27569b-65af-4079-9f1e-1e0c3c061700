package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.domain.dto.UserDTO;
import com.hnguigu.springboot.domain.entity.User;
import com.hnguigu.springboot.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class UserServiceImplTest {

    @Autowired
    private UserService userService;

    @Test
    void testAuthenticate() {
        UserDTO userDTO = new UserDTO();
        userDTO.setName("zhangsan");
        userDTO.setPassword("123456");
        User user = this.userService.authenticate(userDTO);
        System.out.println(user);
    }
}
