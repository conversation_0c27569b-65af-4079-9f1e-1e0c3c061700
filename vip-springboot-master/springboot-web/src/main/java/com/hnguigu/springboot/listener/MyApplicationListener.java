package com.hnguigu.springboot.listener;

import org.springframework.boot.context.event.ApplicationStartingEvent;
import org.springframework.context.ApplicationListener;

/**
 * SpringBoot的监听器
 * 该监听器监听了ApplicationStartingEvent
 * 一旦Spring容器中由EventMulticaster（事件多播器）发布了ApplicationStartingEvent
 * 那么该监听器就能监听到该事件，从而自动调用onApplicationEvent()
 */
public class MyApplicationListener implements ApplicationListener<ApplicationStartingEvent> {

    @Override
    public void onApplicationEvent(ApplicationStartingEvent event) {
        System.out.println("开始干活了");
    }
}
