package com.hnguigu.springboot.interceptor;

import com.hnguigu.springboot.domain.entity.User;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Arrays;
import java.util.List;

public class AuthenticateInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                             Object handler) throws Exception {

        // /api/users/authenticate,/api/users,/api/users/logout 放行
        List<String> excludeUrls = Arrays.asList("/api/users/authenticate", "/api/users/logout");

        // /api/users/findAll
        String requestURI = request.getRequestURI();
        if (excludeUrls.contains(requestURI)) {
            return true;
        }

        HttpSession session = request.getSession(true);
        User user = (User) session.getAttribute("user");
        if (user == null) {
            // 未认证,请重新认证。做了一个重定向到/api/users/authenticate
            response.sendRedirect("/api/users/authenticate");
        }

        return true;
    }
}
