package com.hnguigu.springboot.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.context.annotation.Lazy;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Lazy
public class User implements Serializable {

    private static final long serialVersionUID = -78784960270200781L;
    private Integer id;
    private String name;
    private String password;
}
