package com.hnguigu.springboot.domain.dto;

import com.hnguigu.springboot.domain.entity.User;
import netscape.security.Privilege;

import java.io.Serializable;
import java.util.List;

public class UserInfo implements Serializable {


    private static final long serialVersionUID = 2132539467049653659L;
    private Integer id;
    private String name;
    private String password;

    private List<Privilege> privileges;
}
