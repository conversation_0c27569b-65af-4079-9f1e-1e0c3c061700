package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.domain.dto.UserDTO;
import com.hnguigu.springboot.domain.entity.User;
import com.hnguigu.springboot.service.UserService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class UserServiceImpl implements UserService {

    private static List<User> userList = new ArrayList<>();

    static {
        userList.add(new User(1, "zhangsan", "123456"));
        userList.add(new User(2, "lisi", "123456"));
        userList.add(new User(3, "wangwu", "123456"));
        userList.add(new User(4, "abc", "123456"));
        userList.add(new User(5, "zzz", "123456"));
    }

    @Override
    public User authenticate(UserDTO userDTO) {
        if (ObjectUtils.isEmpty(userDTO)) {
            throw new IllegalArgumentException("userDTO不能为空");
        }

        // 过滤

        /*return userList.stream().filter((user) -> {
            return user.getName().equals(userDTO.getName())
                    && user.getPassword().equals(userDTO.getPassword());
        }).findFirst().get();*/

        for (User user : userList) {
            if (user.getName().equals(userDTO.getName())
                    && user.getPassword().equals(userDTO.getPassword())) {
                return user;
            }
        }

        return null;
    }

    @Override
    public List<User> findAll() {
        return userList;
    }
}
