package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.domain.dto.UserDTO;
import com.hnguigu.springboot.domain.entity.User;
import com.hnguigu.springboot.service.UserService;
import com.hnguigu.springboot.vo.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;

@Controller
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 进入登录页面
     * @return
     */
    @GetMapping("/authenticate")
    public String authenticate() {
        return "authenticate";
    }

    @PostMapping("/authenticate")
    @ResponseBody
    public R authenticate(@RequestBody UserDTO userDTO, HttpSession session) {
        User user = this.userService.authenticate(userDTO);

        // TODO 1.根据当前用户查询该用户的所有权限
        //  1.1
        //  1.2
        // TODO 权限信息和用户基本数据封装到UserInfo对象
        // TODO jjwt 生成Token(载荷 payload)

        // 将认证成功的用户信息设置到Session范围的属性中
        session.setAttribute("user", user);

        return R.success(user);
    }

    @GetMapping("/findAll")
    public R findAll() {
        return R.success(this.userService.findAll());
    }


}
