package com.hnguigu.springboot.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
//@ServletComponentScan(value = {"com.hnguigu.springboot.filter", "com.hnguigu.springboot
// .listener"})
public class WebConfig implements WebMvcConfigurer {

    /**
     * @param registry 注册中心
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 这行代码不要注释
//        WebMvcConfigurer.super.addInterceptors(registry);
//        registry.addInterceptor(new DemoInterceptor());
//        registry.addInterceptor(new AuthenticateInterceptor());
    }

    /**
     * 自定义静态资源映射
     *
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        WebMvcConfigurer.super.addResourceHandlers(registry);

        // mapping k-v
        registry.addResourceHandler("/a/**", "/b/**").addResourceLocations("classpath:/a/");
    }

    /*@Bean
    public FilterRegistrationBean filterRegistrationBean1() {
        FilterRegistrationBean<DemoFilter1> registrationBean = new FilterRegistrationBean<>();
        DemoFilter1 demoFilter1 = new DemoFilter1();

        registrationBean.setFilter(demoFilter1);
        registrationBean.setUrlPatterns(Arrays.asList("/*"));
        registrationBean.setDispatcherTypes(EnumSet.of(DispatcherType.REQUEST));
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean filterRegistrationBean2() {
        FilterRegistrationBean<DemoFilter2> registrationBean = new FilterRegistrationBean<>();
        DemoFilter2 demoFilter2 = new DemoFilter2();

        registrationBean.setFilter(demoFilter2);
        registrationBean.setUrlPatterns(Arrays.asList("/*"));
        registrationBean.setDispatcherTypes(EnumSet.of(DispatcherType.REQUEST));
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean filterRegistrationBean3() {
        FilterRegistrationBean<DemoFilter3> registrationBean = new FilterRegistrationBean<>();
        DemoFilter3 demoFilter3 = new DemoFilter3();

        registrationBean.setFilter(demoFilter3);
        registrationBean.setUrlPatterns(Arrays.asList("/*"));
        registrationBean.setDispatcherTypes(EnumSet.of(DispatcherType.REQUEST));
        return registrationBean;
    }

    @Bean
    public ServletListenerRegistrationBean servletListenerRegistrationBean() {
        ServletListenerRegistrationBean<DemoListener> registrationBean =
                new ServletListenerRegistrationBean<>();
        registrationBean.setListener(new DemoListener());
        return registrationBean;
    }*/
}
