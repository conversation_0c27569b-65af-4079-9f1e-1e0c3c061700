package com.hnguigu.springboot.listener;

import org.springframework.boot.ConfigurableBootstrapContext;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.SpringApplicationRunListener;

public class MySpringApplicationRunListener implements SpringApplicationRunListener {

    private SpringApplication application;

    private String[] args;

    public MySpringApplicationRunListener() {
    }

    public MySpringApplicationRunListener(SpringApplication application, String[] args) {
        this.application = application;
        this.args = args;
    }

    @Override
    public void starting(ConfigurableBootstrapContext bootstrapContext) {
        SpringApplicationRunListener.super.starting(bootstrapContext);
        System.out.println("Spring Application 正在启动");
    }
}
