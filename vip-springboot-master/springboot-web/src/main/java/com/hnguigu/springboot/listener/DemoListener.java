package com.hnguigu.springboot.listener;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

@WebListener
public class DemoListener implements ServletContextListener {

//    private List<User> onlineList = Collections.synchronizedList(new ArrayList<>());

    /**
     * 容器初始化
     *
     * @param sce Information about the ServletContext that was initialized
     */
    @Override
    public void contextInitialized(ServletContextEvent sce) {
        System.out.println("容器初始化");

        // TODO 监听器的应用场景
        // 1. 初始化数据
        // 2. 实现在线人员统计
//        sce.getServletContext().setAttribute("count", 0);
//        sce.getServletContext().setAttribute("onlineList", onlineList);
    }

    /**
     * 容器销毁
     *
     * @param sce Information about the ServletContext that was destroyed
     */
    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        System.out.println("容器销毁");
    }
}
