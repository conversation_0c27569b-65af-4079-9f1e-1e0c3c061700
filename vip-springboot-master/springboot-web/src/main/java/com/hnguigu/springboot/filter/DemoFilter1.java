package com.hnguigu.springboot.filter;


import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 所有的请求都会过滤
 */
@WebFilter(filterName = "demoFilter1" ,value = "/*")
public class DemoFilter1 implements Filter {

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse,
                         FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;


        // TODO 过滤的真正逻辑
        System.out.println("过滤器1执行了");

        // 必须编写。请求就不会向下执行
        filterChain.doFilter(request,response);

        System.out.println("过滤器1执行完毕");
    }
}
