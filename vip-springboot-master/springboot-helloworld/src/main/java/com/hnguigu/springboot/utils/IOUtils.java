package com.hnguigu.springboot.utils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;

public class IOUtils {

    /**
     * 方法泛型
     *
     * @param t
     * @param <T>
     */
    public static <T> void save(T t) {
        ObjectOutputStream objectOutputStream = null;
        FileOutputStream fileOutputStream = null;

        if (t == null) {
            throw new IllegalArgumentException("t must not be null");
        }

        try {
            fileOutputStream = new FileOutputStream("user.obj");

            // 将文件输出流装饰成对象输出流
            objectOutputStream = new ObjectOutputStream(fileOutputStream);

            objectOutputStream.writeObject(t);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (objectOutputStream != null) {
                    objectOutputStream.close();
                }

                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
