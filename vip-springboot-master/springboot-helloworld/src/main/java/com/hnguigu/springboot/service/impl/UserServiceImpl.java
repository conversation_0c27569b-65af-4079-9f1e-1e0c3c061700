package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

@Service
public class UserServiceImpl implements UserService {

    private static List<User> userList = new ArrayList<>();

    static {
        userList.add(new User(1000, "zhangsan", "admin", 5000.0f, new Date()));
        userList.add(new User(2000, "lisi", "admin", 6000.0f, new Date()));
        userList.add(new User(3000, "wangwu", "admin", 7000.0f, new Date()));
    }

    @Override
    public void modifyUser(User user) {
        for (User u : userList) {
            if (u.getId().equals(user.getId())) {
                BeanUtils.copyProperties(user, u);

                /*u.setName(user.getName());
                u.setPassword(user.getPassword());
                u.setSalary(user.getSalary());
                u.setBirthday(user.getBirthday());*/
            }
        }
    }

    @Override
    public void deleteUserById(Integer id) {
        if (ObjectUtils.isEmpty(id)) {
            throw new IllegalArgumentException("id不能为空");
        }

        /*for (User user : userList) {
            if (user.getId().equals(id)) {
                userList.remove(user);
            }
        }*/

        // 为什么建议使用迭代器删除，而不直接使用集合删除
        for (Iterator<User> iterator = userList.iterator(); iterator.hasNext(); ) {
            User user = iterator.next();
            if (user.getId().equals(id)) {
                // 内存的删除
                iterator.remove();
                break;
            }
        }

        // 持久化 序列化（概念）操作 ObjectOutputStream#writeObject
        // 对象进行持久化到介质中（文件，磁盘，网络）
//        save(userList);
    }
}
