package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/helloworld")
@Slf4j
public class HelloworldController {

    @Autowired
    private UserService userService;

    @GetMapping("/test")
    public String test() {
        log.info("你好，我进来了");
//        System.out.println("你好，我进来了");
        return "success";
    }

    @GetMapping("/{id}")
    public User findById(@PathVariable Integer id) {
        User user = new User();
        user.setId(id);
        user.setName("zhangsan");
        user.setPassword("admin");
        user.setSalary(11.11F);
        user.setBirthday(new Date());
        return user;
    }

    @PostMapping
    public String add(@RequestBody User user) {
        log.info("添加用户：{}", user);
        return "success";
    }


    @DeleteMapping("/{id}")
    public String delete(@PathVariable Integer id) {
        this.userService.deleteUserById(id);
        return "success";
    }

    @PutMapping
    public String update(@RequestBody User user) {
        this.userService.modifyUser(user);
        return "success";
    }


}
