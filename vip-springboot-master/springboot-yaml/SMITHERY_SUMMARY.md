# Smithery.ai 网站总结

Smithery.ai 是一个为智能体（Agent）扩展能力的平台，核心通过 Model Context Protocol (MCP) 服务器，提供了超过6,000种能力扩展。其主要面向 AI 助手、自动化工具和开发者，帮助它们无缝集成各种外部服务和工具。

## 主要功能
- **MCP能力扩展**：通过注册和调用 MCP 服务器，智能体可以动态获得新的能力，如文档检索、自动化、Web搜索、数据库操作、记忆管理等。
- **一站式能力市场**：Smithery.ai 汇聚了丰富的能力插件，涵盖开发、自动化、搜索、记忆、浏览器自动化、应用集成等多个领域。
- **分类清晰**：能力分为"特色（Featured）"、"热门（Popular）"、"Web搜索"、"记忆管理"、"浏览器自动化"、"应用集成"等，便于用户快速查找所需功能。

## 特色能力（Featured）
- **Context7**：获取最新、特定版本的文档和代码示例，消除过时信息。
- **Clear Thought**：提升系统性思考和问题解决能力。
- **Discord/Slack**：无缝集成主流协作平台，实现消息管理和自动化。
- **Toolbox**：智能路由所有 MCP 能力，自动提示配置。

## 热门能力（Popular）
- **Desktop Commander**：本地终端命令与文件管理，支持代码、Shell、自动化等。
- **Sequential Thinking**：结构化思考与反思，适合复杂问题求解。
- **Browserbase**：云端浏览器自动化，支持网页交互、截图、脚本执行。
- **DuckDuckGo/Brave/Exa/Kagi/Perplexity 搜索**：多种 Web 搜索能力，满足不同场景需求。
- **Notion API**：集成 Notion 数据库和任务管理。
- **Memory Tool/Memory Bank/Knowledge Graph Memory**：多种记忆管理方案，支持上下文记忆、知识图谱等。

## 其他能力分类
- **Web搜索**：集成多种主流和新兴搜索引擎。
- **记忆管理**：支持用户偏好、上下文、知识的存储与检索。
- **浏览器自动化**：支持 Playwright、Puppeteer、Browserbase 等自动化方案。
- **应用集成**：如 Apple 工具、Metabase、Ableton Live、Discord、Notion 等。

## 适用场景
- AI 助手能力扩展
- 自动化开发与运维
- 智能体上下文记忆与知识管理
- 多平台集成与数据互通

## 参考链接
- 官网: [https://smithery.ai/](https://smithery.ai/)
- Model Context Protocol: [https://modelcontextprotocol.io/](https://modelcontextprotocol.io/) 