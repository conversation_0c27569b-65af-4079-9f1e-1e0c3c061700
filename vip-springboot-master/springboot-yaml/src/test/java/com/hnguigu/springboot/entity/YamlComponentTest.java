package com.hnguigu.springboot.entity;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

// SpringBoot与JUnit进行了整合
@SpringBootTest
class YamlComponentTest {

    @Autowired
    private YamlComponent yamlComponent;

    // test()就是一个单元测试方法
    @Test
    public void test() {
        System.out.println(this.yamlComponent);
    }

}
