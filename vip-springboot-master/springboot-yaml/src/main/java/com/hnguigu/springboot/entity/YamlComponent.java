package com.hnguigu.springboot.entity;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
// 纳入spring容器
@Component
@ConfigurationProperties(prefix = "yaml.test")
public class YamlComponent implements Serializable {

    private static final long serialVersionUID = 551094724659197175L;

    private String userName;
    private Boolean boss;
    private Date birth;
    private Integer age;
    private Other other;
    private String[] interests;
    private List<String> animal;
    private Map<String, Object> score;
    private Set<Double> salary;
    private Map<String, List<Other>> allOthers;

}
