#user:
#  name: <PERSON><PERSON><PERSON>
#  password: admin
#  age: 23
#  birthday: 2023/07/10
#  flag: true
#
#person:
#  #  friend: { 'name': 'lisi','age': 33 }
#
#  friend:
#    name: lisi
#    age: 33
#  inters:
#    - "football"
#    - "basketball"
#    - "volleyball"
#  hobbies: [ "swimming", "running" ]
#
#  #  employees:
#  #    - { "name": "abc",age:23 }
#  #    - { "name": "aaa",age:22 }
#  #    - { "name": "bbb",age:55 }
#
#  employees:
#    - id: 1,
#      name: "aaa",
#      age: 23
#    - id: 2,
#      name: "nnn",
#      age: 53
#    - { id:3,"name": "bbb",age:55 }
#
#  null: ~    # null
#  empty: ""  # 空字符串
#  message: |
#    hello
#    world
#    ! \fsd\
#    fds
#    f
#    ds
#    fsd
#    fds
#
#  default: &defaults    #复用 重复使用
#    adapter: postgres
#    host: localhost
#
#  development:
#    database: myapp_development
#    <<: *defaults
#
#  test:
#    database: myapp_test
#    <<: *defaults

yaml:
  test:
    userName: z<PERSON><PERSON>
    boss: false
    birth: 1234/12/12
    age: 23
    other: { name: "lisi",count: 100 }
    interests:
      - football
      - basketball
      - volleyball'
    animal: [ "cat","dog","bird" ]
    score:
      key1: "k1"
      key2: "k2"
    salary: [ 12.34,23.34,55.55 ]
    allOthers:
      k1: [ { name: "lisi",count: 100 },{ name: "wangwu",count:200 } ]
      k2:
        -
          name: "zhangfei"
          count: 300
        -
          name: "guanyu"
          count: 400
        - { name: "machao",count: 500 }





















