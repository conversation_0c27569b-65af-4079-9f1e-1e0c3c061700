package com.hnguigu.springboot.exception;

import com.hnguigu.springboot.vo.R;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 异常处理解析器
 */
//@RestControllerAdvice
public class MyExceptionHandlerResolver1 {

    @ExceptionHandler(MyException.class)
    public R handleException(MyException exception) {
        return R.error(exception.getMessage());
    }
}
