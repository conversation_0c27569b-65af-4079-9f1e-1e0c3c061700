package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.exception.MyException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/api/exception")
public class ExceptionController {

    @GetMapping
    public String test() {
        /*try {
            int i = 1 / 0;
        } catch (Exception e) {
            throw new MyException("除零了");
        }*/

        int i = 1 / 0;
        return "success";
    }
}
