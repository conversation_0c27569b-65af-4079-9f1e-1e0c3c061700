package com.hnguigu.springboot.exception;

import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 异常处理解析器
 */
public class MyExceptionHandlerResolver implements HandlerExceptionResolver {

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response
            , Object handler, Exception ex) {
        ModelAndView modelAndView = new ModelAndView();

        if (ex instanceof MyException) {
            MyException myException = (MyException) ex;
            String message = myException.getMessage();
            modelAndView.addObject("errorMessage", message);
            modelAndView.setViewName("error");
        }
        return modelAndView;
    }
}
