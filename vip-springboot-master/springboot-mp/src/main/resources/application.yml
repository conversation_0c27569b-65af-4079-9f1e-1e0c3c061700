server:
  port: 8888

spring:
  application:
    name: springboot-mybatis
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      test-on-borrow: true
      test-on-return: true
      test-while-idle: true
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *****************************************************************************************************
      username: root
      password: root

mybatis-plus:
  configuration:
    aggressive-lazy-loading: false
    lazy-loading-enabled: true
    cache-enabled: true
    map-underscore-to-camel-case: true
  mapper-locations: classpath:mapper/*Mapper.xml
  type-aliases-package: com.hnguigu.springboot.entity





