package com.hnguigu.springboot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("mybatis_department")
public class Department implements Serializable {

    private static final long serialVersionUID = 6082734003889496314L;

    @TableId(value = "department_id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "department_name")
    private String name;

    @TableField(value = "department_location")
    private String location;
}
