package com.hnguigu.springboot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("mybatis_user")
public class User implements Serializable {

    private static final long serialVersionUID = 6550995370786726671L;

    // type属性：主键生成策略
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long id;

    @TableField("user_name")
    private String name;

    @TableField("user_password")
    private String password;

    @TableField("user_salary")
    private Float salary;

    @TableField("user_birthday")
    private Date birthday;

    @TableField("department_id")
    private Long departmentId;
}
