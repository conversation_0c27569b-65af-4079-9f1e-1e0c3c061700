package com.hnguigu.springboot.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnguigu.springboot.entity.Department;
import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.service.DepartmentService;
import com.hnguigu.springboot.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.CollectionUtils;

import java.util.List;

@SpringBootTest
class DepartmentServiceImplTest {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private UserService userService;

    @Test
    public void testAdd() {
        Department department = new Department();
        department.setName("Apple");
        department.setLocation("LOS");
        this.departmentService.save(department);
    }

    @Test
    public void testDelete() {
        Department department = this.departmentService.getById(1L);
        if (department != null) {

            // 查询部门下的用户
            /*QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("department_id", department.getId());
            List<User> userList = this.userService.listObjs(queryWrapper);*/

            /*LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            // User::getDepartmentId ---> 方法引用
            lambdaQueryWrapper.eq(User::getDepartmentId, department.getId());
            List<User> userList = this.userService.listObjs(lambdaQueryWrapper);*/

            // 链式编程 Fluent API
            /*List<User> userList = this.userService.listObjs(new QueryWrapper<User>()
                    .lambda()
                    .eq(User::getDepartmentId, department.getId()));*/

            // 使用Wrappers工具类
            List<User> userList =
                    this.userService.list(Wrappers.lambdaQuery(User.class).eq(User::getDepartmentId, department.getId()));


            if (CollectionUtils.isEmpty(userList)) {
                this.departmentService.removeById(department);
            }
        }
    }

    @Test
    public void testUpdate() {

    }

    @Test
    public void testFindById() {
        Department department = this.departmentService.getById(1L);
        System.out.println(department);

        // Optional避免 NPE问题（NullPointerException）
        /*Optional<Department> optional = this.departmentService.getOptById(10L);
        Department department1 = optional.get();
        System.out.println(department1);*/

    }

    @Test
    public void testFindPage() {
        // 多态
        IPage<Department> page = new Page<>(2, 2);
        page = this.departmentService.page(page);

        // 每页显示记录数
        System.out.println(page.getSize());

        // 当前第几页
        System.out.println(page.getCurrent());

        // 当前这页的数据
        System.out.println(page.getRecords());

        // 总记录数
        System.out.println(page.getTotal());

        // 总页数
        System.out.println(page.getPages());


    }

}
