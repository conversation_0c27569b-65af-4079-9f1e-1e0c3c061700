package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.service.UserService;
import com.hnguigu.springboot.utils.IOUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Service
public class UserServiceImpl implements UserService {

    private static List<User> userList = Collections.synchronizedList(new ArrayList<>());

//    private static Set<User> userSet = Collections.synchronizedSet(new HashSet<>());

    // static 只有一份 单例 共享资源 多线程
//    private static List<User> userList = new ArrayList<>();

    static {
        userList.add(new User(1000, "zhangsan", "admin", 5000.0f, new Date()));
        userList.add(new User(2000, "lisi", "admin", 6000.0f, new Date()));
        userList.add(new User(3000, "wangwu", "admin", 7000.0f, new Date()));
    }

    @Override
    public void addUser(User user) {
        if (ObjectUtils.isEmpty(user)) {
            throw new IllegalArgumentException("user不能为空");
        }

        // 先从文件中反序列化出集合
        List<User> users = IOUtils.load("users.obj");
        // 1. 如果users.obj文件不存在，创建文件
        // 2. 如果文件存在，将文件中的数据读取到集合中

        users.add(user);

        // 将集合进行序列化操作
        IOUtils.save(users);
    }

    @Override
    public void deleteUser(User user) {
        if (ObjectUtils.isEmpty(user)) {
            throw new IllegalArgumentException("user不能为空");
        }

        // 先从文件中反序列化出集合
        List<User> users = IOUtils.load("users.obj");


        for (Iterator<User> iterator = users.iterator(); iterator.hasNext(); ) {
            User u = iterator.next();
            if (u.getId().equals(user.getId())) {
                iterator.remove();
            }
        }

        IOUtils.save(users);
    }

    @Override
    public void modifyUser(User user) {

    }

    @Override
    public User findUserById(Integer id) {
        return null;
    }

    @Override
    public List<User> findAll() {
        return Collections.emptyList();
    }
}
