package com.hnguigu.springboot.utils;

import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.*;

public class IOUtils {

    /**
     * 序列化
     *
     * @param t
     * @param <T>
     */
    public static <T> void save(T t) {
        ObjectOutputStream objectOutputStream = null;
        FileOutputStream fileOutputStream = null;

        if (ObjectUtils.isEmpty(t)) {
            throw new IllegalArgumentException("t must not be null");
        }

        try {
            fileOutputStream = new FileOutputStream("users.obj");
            objectOutputStream = new ObjectOutputStream(fileOutputStream);
            objectOutputStream.writeObject(t);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (objectOutputStream != null) {
                    objectOutputStream.close();
                }

                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 反序列化
     *
     * @return
     */
    public static <T> T load(String filePath) {
        ObjectInputStream objectInputStream = null;
        FileInputStream fileInputStream = null;
        T t = null;

        if (StringUtils.isEmpty(filePath)) {
            throw new IllegalArgumentException("filePath must not be null");
        }

        try {
            fileInputStream = new FileInputStream(filePath);
            objectInputStream = new ObjectInputStream(fileInputStream);
            t = (T) objectInputStream.readObject();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (objectInputStream != null) {
                    objectInputStream.close();
                }

                if (fileInputStream != null) {
                    fileInputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return t;
    }
}
