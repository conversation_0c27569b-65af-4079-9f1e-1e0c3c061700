import requests
import os
from datetime import datetime

def scrape_and_save_markdown():
    """简单抓取网页内容并保存为Markdown文件"""

    url = "https://linux.do/"

    try:
        # 发送HTTP请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # 创建Markdown内容
        md_content = []
        md_content.append("# Linux.do 首页内容抓取报告\n")
        md_content.append(f"**抓取时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        md_content.append(f"**源URL**: [{url}]({url})\n\n")

        # 添加原始HTML内容（简化版）
        html_content = response.text[:2000]  # 只取前2000字符
        md_content.append("## 网页内容预览\n")
        md_content.append("```\n")
        md_content.append(html_content)
        md_content.append("\n```\n\n")

        # 提取一些基本信息
        md_content.append("## 页面基本信息\n")
        md_content.append(f"- 状态码: {response.status_code}\n")
        md_content.append(f"- 内容长度: {len(response.text)} 字符\n")
        md_content.append(f"- 内容类型: {response.headers.get('content-type', 'Unknown')}\n\n")

        # 写入文件
        filename = f"linux_do_content_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(md_content))

        print(f"成功生成文件: {filename}")
        return filename

    except Exception as e:
        print(f"抓取过程中出现错误: {str(e)}")
        # 即使出错也创建一个基础的Markdown文件
        filename = f"linux_do_content_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        error_content = f"""# Linux.do 首页内容抓取报告

**抓取时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**源URL**: https://linux.do/

## 错误信息
抓取过程中出现错误: {str(e)}

## 建议
- 请检查网络连接
- 该网站可能有反爬虫机制
- 可以尝试使用代理或调整请求头
"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(error_content)
        print(f"已生成错误报告文件: {filename}")
        return filename

if __name__ == "__main__":
    scrape_and_save_markdown()
