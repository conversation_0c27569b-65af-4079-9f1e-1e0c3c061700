# Spring Data MongoDB 去除 _class 字段指南

## 问题描述

Spring Data MongoDB 默认会在每个文档中添加一个 `_class` 字段，用于存储 Java 类的完全限定名。这个字段的作用是：

1. 支持多态映射
2. 确保反序列化时能正确创建对象类型
3. 处理继承关系

但在很多情况下，我们不需要这个字段，它会：
- 增加文档大小
- 暴露 Java 类结构信息
- 影响与其他系统的数据交换

## 解决方案

### 方案一：配置 MongoTemplate（推荐）

在 `src/main/java/com/hnguigu/springboot/config/MongoConfig.java` 中配置：

```java
@Configuration
public class MongoConfig {

    @Autowired
    private MappingMongoConverter mappingMongoConverter;

    @Bean
    public MongoTemplate mongoTemplate(MongoDatabaseFactory mongoDatabaseFactory) {
        // 设置 typeMapper 为 null，禁用 _class 字段
        mappingMongoConverter.setTypeMapper(new DefaultMongoTypeMapper(null));
        return new MongoTemplate(mongoDatabaseFactory, mappingMongoConverter);
    }
}
```

### 方案二：通过 application.yml 配置

在 `application.yml` 中添加：

```yaml
spring:
  data:
    mongodb:
      field-naming-strategy: org.springframework.data.mapping.model.SnakeCaseFieldNamingStrategy
      type-key: null  # 禁用类型键
```

### 方案三：自定义 MappingMongoConverter

```java
@Configuration
public class MongoConfig extends AbstractMongoClientConfiguration {

    @Override
    public MappingMongoConverter mappingMongoConverter() throws Exception {
        MappingMongoConverter converter = super.mappingMongoConverter();
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        return converter;
    }
}
```

## 测试验证

1. 启动应用程序
2. 使用 POST 请求创建一个 Person：

```bash
curl -X POST http://localhost:9999/api/person \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "张三",
    "age": 25,
    "address": "北京市",
    "description": "测试用户"
  }'
```

3. 在 MongoDB 中查看文档，应该不会包含 `_class` 字段

## 注意事项

1. **多态支持**：去除 `_class` 字段后，将无法支持多态映射
2. **继承关系**：如果实体类有继承关系，可能会影响反序列化
3. **类型安全**：确保所有操作的实体类型一致
4. **数据迁移**：如果已有数据包含 `_class` 字段，需要考虑数据清理

## 验证方法

连接到 MongoDB 并查看集合：

```javascript
// 连接到 MongoDB
use demo

// 查看 person 集合中的文档
db.person.find().pretty()

// 应该看到类似这样的文档（没有 _class 字段）：
{
  "_id": ObjectId("..."),
  "id": 1,
  "name": "张三",
  "age": 25,
  "address": "北京市",
  "description": "测试用户"
}
```

## 推荐使用

建议使用**方案一**，因为它：
- 配置简单
- 兼容性好
- 不影响其他 Spring Data MongoDB 功能
- 易于理解和维护
