package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.Person;
import com.hnguigu.springboot.service.PersonService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class PersonServiceImplTest {

    @Autowired
    private PersonService personService;

    @org.junit.jupiter.api.Test
    void addPerson() {
        Person person = new Person();
        person.setId(1006L);
        person.setName("关羽");
        person.setAge(14);
        person.setAddress("上海朝阳区");
        person.setDescription("关羽工作很认真");
        this.personService.addPerson(person);
    }

    @org.junit.jupiter.api.Test
    void testFindPersonById() {
        Person person = this.personService.findPersonById(1005L);
        System.out.println(person);
    }

    @Test
    public void testAddPersons() {
        List<Person> personList = new ArrayList<>();
        personList.add(new Person(1007L, "刘备", 15, "上海浦东新区", "刘备工作很认真"));
        personList.add(new Person(1008L, "马超", 16, "杭州浦东新区", "马超工作很认真"));
        personList.add(new Person(1009L, "赵云", 17, "苏州浦东新区", "赵云工作很认真"));
        this.personService.addPersons(personList);
    }

    @Test
    public void testDeletePerson() {
        this.personService.deletePerson(this.personService.findPersonById(1007L));
    }

    @Test
    public void testUpdatePerson() {
        Person person = this.personService.findPersonById(1008L);
        person.setName("黄忠");
        this.personService.updatePerson(person);
    }

    @Test
    public void testFindPage() {
        Page<Person> page = this.personService.findPage(2, 2);
        System.out.println(page);
    }

    @Test
    public void testFindByNameAndAddress() {
        List<Person> personList = this.personService.findPersonListByNameAndAddress("马超", "杭州浦东新区");
        personList.stream().forEach(System.out::println);

    }
}
