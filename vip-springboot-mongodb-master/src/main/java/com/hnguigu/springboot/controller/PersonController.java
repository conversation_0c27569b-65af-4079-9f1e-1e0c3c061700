package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.entity.Person;
import com.hnguigu.springboot.repository.PersonRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Person 控制器
 * 用于测试 MongoDB 操作，验证是否去除了 _class 字段
 */
@RestController
@RequestMapping("/api/person")
public class PersonController {

    @Autowired
    private PersonRepository personRepository;

    /**
     * 创建一个新的 Person
     */
    @PostMapping
    public Person createPerson(@RequestBody Person person) {
        return personRepository.save(person);
    }

    /**
     * 获取所有 Person
     */
    @GetMapping
    public List<Person> getAllPersons() {
        return personRepository.findAll();
    }

    /**
     * 根据 ID 获取 Person
     */
    @GetMapping("/{id}")
    public Person getPersonById(@PathVariable String id) {
        return personRepository.findById(id).orElse(null);
    }

    /**
     * 删除所有 Person（用于测试）
     */
    @DeleteMapping("/all")
    public String deleteAll() {
        personRepository.deleteAll();
        return "All persons deleted";
    }
}
