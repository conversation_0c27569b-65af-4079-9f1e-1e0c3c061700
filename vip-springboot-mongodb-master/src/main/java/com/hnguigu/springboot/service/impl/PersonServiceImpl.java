package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.Person;
import com.hnguigu.springboot.repository.PersonRepository;
import com.hnguigu.springboot.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
public class PersonServiceImpl implements PersonService {

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void addPerson(Person person) {
        this.personRepository.save(person);
    }

    @Override
    public void addPersons(List<Person> personList) {
        if (CollectionUtils.isEmpty(personList)) {
            throw new IllegalArgumentException("personList must not be empty");
        }

        this.personRepository.insert(personList);
    }

    @Override
    public void deletePerson(Person person) {
        this.personRepository.delete(person);
    }

    @Override
    public void updatePerson(Person person) {
        this.personRepository.save(person);
    }

    @Override
    public Person findPersonById(Long id) {
        return this.personRepository.getById(id);
    }

    @Override
    public List<Person> findAllPersons() {
        return this.personRepository.findAll();
    }

    @Override
    public Page<Person> findPage(Integer pageNum, Integer pageSize) {
        if (ObjectUtils.isEmpty(pageNum) || ObjectUtils.isEmpty(pageSize)) {
            throw new IllegalArgumentException("pageNum and pageSize must not be null");
        }

        PageRequest pageRequest = PageRequest.of(pageNum - 1, pageSize);
        return this.personRepository.findAll(pageRequest);
    }

    @Override
    public List<Person> findPersonListByNameAndAddress(String name, String address) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(address)) {
            throw new IllegalArgumentException("name and address must not be empty");
        }
//        return this.personRepository.findByNameAndAddress(name, address);
//        return this.personRepository.findByNameAndAddressLike(name, address);

        /*String queryCondition = "{'name':" + name + ", 'address': " + address + "}";
        BasicQuery query = new BasicQuery(queryCondition);
        return this.mongoTemplate.find(query, Person.class);*/

        // QBC 基于标准化查询
        /*Query query = new Query();
        query.addCriteria(Criteria.where("name").is(name));
        query.addCriteria(Criteria.where("address").is(address));
        return this.mongoTemplate.find(query, Person.class);*/

        // QBE 基于样例查询
        Person person = new Person();
        person.setName(name);
        person.setAddress(address);
        Query query = Query.query(Criteria.byExample(person));
        return this.mongoTemplate.find(query, Person.class);


    }
}
