package com.hnguigu.springboot.service;

import com.hnguigu.springboot.entity.Person;
import org.springframework.data.domain.Page;

import java.util.List;

public interface PersonService {

    void addPerson(Person person);

    void addPersons(List<Person> personList);

    void deletePerson(Person person);

    void update<PERSON>erson(Person person);

    Person findPersonById(Long id);

    List<Person> findAllPersons();

    Page<Person> findPage(Integer pageNum, Integer pageSize);

    List<Person> findPersonListByNameAndAddress(String name, String address);
}
