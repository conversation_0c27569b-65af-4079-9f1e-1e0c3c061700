package com.hnguigu.springboot.repository;

import com.hnguigu.springboot.entity.Person;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface PersonRepository extends MongoRepository<Person, String> {

    /**
     * 命名查询
     *
     * @param id
     * @return
     */
    Person getById(Long id);

    /**
     * 自定义查询
     *
     * @param name
     * @param address
     * @return
     */
    List<Person> findByNameAndAddressLike(String name, String address);

    List<Person> findByNameAndAddress(String name, String address);

}
