package com.hnguigu.springboot.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.io.Serializable;

@Data
@NoArgsConstructor
@Document(value = "person")
public class Person implements Serializable {

    private static final long serialVersionUID = 954483839960695342L;

    @Id
    @Field(value = "_id", targetType = FieldType.OBJECT_ID)
    private String _id;

    @Field(value = "id", targetType = FieldType.INT32)
    private Long id;

    @Field(value = "name")
    private String name;

    @Field(value = "age")
    private Integer age;

    @Field(value = "address")
    private String address;

    @Field(value = "description")
    private String description;

    public Person(Long id, String name, Integer age, String address, String description) {
        this.id = id;
        this.name = name;
        this.age = age;
        this.address = address;
        this.description = description;
    }
}
