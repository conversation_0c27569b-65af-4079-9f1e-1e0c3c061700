import requests
from bs4 import BeautifulSoup
import markdown
import os
from datetime import datetime

def scrape_linux_do():
    """抓取https://linux.do/首页内容并生成Markdown文件"""

    url = "https://linux.do/"

    try:
        # 发送HTTP请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # 解析HTML内容
        soup = BeautifulSoup(response.content, 'html.parser')

        # 创建Markdown内容
        md_content = []
        md_content.append("# Linux.do 首页内容抓取报告\n")
        md_content.append(f"**抓取时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        md_content.append(f"**源URL**: [{url}]({url})\n\n")

        # 抓取标题
        title = soup.find('title')
        if title:
            md_content.append(f"## 页面标题\n{title.text.strip()}\n\n")

        # 抓取主要导航链接
        md_content.append("## 主要导航链接\n")
        nav_links = soup.find_all('a', class_='nav-link')
        for link in nav_links[:10]:  # 只取前10个链接
            href = link.get('href', '')
            text = link.get_text(strip=True)
            if href and text:
                md_content.append(f"- [{text}]({href})")
        md_content.append("\n\n")

        # 抓取论坛主题列表
        md_content.append("## 最新主题\n")
        topic_elements = soup.find_all('a', class_='title')
        if topic_elements:
            for i, topic in enumerate(topic_elements[:10]):  # 只取前10个主题
                href = topic.get('href', '')
                text = topic.get_text(strip=True)
                if href and text:
                    md_content.append(f"{i+1}. [{text}]({href})")
            md_content.append("\n\n")
        else:
            # 如果没有找到特定class，尝试其他方式
            topics = soup.find_all('div', class_='topic-title')
            for i, topic in enumerate(topics[:10]):
                title_link = topic.find('a')
                if title_link:
                    href = title_link.get('href', '')
                    text = title_link.get_text(strip=True)
                    if href and text:
                        md_content.append(f"{i+1}. [{text}]({href})")
            md_content.append("\n\n")

        # 抓取页面主要内容区域
        md_content.append("## 页面主要内容\n")
        main_content = soup.find('main') or soup.find('div', class_='container')
        if main_content:
            # 提取文本内容
            text_content = main_content.get_text(strip=True)[:500]  # 只取前500字符
            md_content.append(f"```\n{text_content}\n```\n\n")
        else:
            # 如果找不到特定区域，提取所有文本
            all_text = soup.get_text(strip=True)[:500]
            md_content.append(f"```\n{all_text}\n```\n\n")

        # 抓取页脚信息
        md_content.append("## 页脚信息\n")
        footer = soup.find('footer')
        if footer:
            footer_text = footer.get_text(strip=True)[:300]
            md_content.append(f"```\n{footer_text}\n```\n\n")
        else:
            md_content.append("未找到页脚信息\n\n")

        # 写入文件
        filename = f"linux_do_content_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(md_content))

        print(f"成功生成文件: {filename}")
        return filename

    except Exception as e:
        print(f"抓取过程中出现错误: {str(e)}")
        return None

if __name__ == "__main__":
    scrape_linux_do()
