<script setup lang="ts">

// 定义了一个类型

import type {GoodsType} from "../types/data.d.ts";

type Emits = {
  (e: 'get-message', msg: string): void,
  (e: 'get-list', goodsList: GoodsType[]): void
}

let emits = defineEmits<Emits>();

const handleSend = () => {
  emits('get-message', 'Hello World')
}

const handleSend1 = () => {
  emits('get-list', [{'id': 1001, 'name': '冬季棉袜'}])
}

</script>

<template>
  <div>
    <button @click="handleSend()">发送数据到父组件</button>
    <button @click="handleSend1()">发送数据到父组件1</button>
  </div>
</template>

<style scoped>

</style>
