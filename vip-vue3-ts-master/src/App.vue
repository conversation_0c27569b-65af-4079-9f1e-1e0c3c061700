<script lang="ts" setup>

import MySon2 from "./components/MySon2.vue";
import type {GoodsType} from "./types/data.d.ts";

const getMessage = (msg: string) => {
  console.log('子组件向父组件传过来的值为', msg)
}

const getGoodsList = (goodsList: GoodsType[]) => {
  goodsList.forEach(item => {
    console.log(item.id, item.name, item.price)
  })
}


</script>

<template>
  <div>Helloworld Vue3 + TS</div>
  <my-son2 @get-message="getMessage" @get-list="getGoodsList"></my-son2>

</template>

<style scoped>
</style>
