import {defineStore} from "pinia";
import {computed, ref} from "vue";
import axios from "axios";
import type {ChannelRes} from "../types";

// defineStore() 有两个参数。第一个参数是仓库的名称，第二个参数是配置对象
export const useCountStore = defineStore('count', () => {
    // 定义state
    const count = ref<number>(0)
    const channelRes = ref<ChannelRes>()

    // 定义方法(同步和异步)
    const addCount = () => {
        count.value++
    }

    const getChannelList = async () => {
        const res = await axios.get('http://geek.itheima.net/v1_0/channels')
        channelRes.value = res.data
    }

    // getter
    const doubleCount = computed(() => {
        return count.value * 2
    })

    // 返回state和方法
    return {
        count,
        channelRes,
        doubleCount,
        addCount,
        getChannelList
    }
})


