# Project Context for Qwen Code

## Project Overview
This is a Vue 3 + TypeScript + Vite project that demonstrates the usage of Pinia for state management. The project is a simple counter application that also fetches channel data from an API to showcase both local state management and asynchronous operations with Pinia.

### Main Technologies
- **Vue 3** with `<script setup>` SFCs
- **TypeScript** for type safety
- **Vite** as the build tool
- **Pinia** for state management
- **Axios** for HTTP requests

### Project Structure
```
src/
├── assets/          # Static assets (currently empty)
├── components/      # Reusable Vue components (currently empty)
├── stores/          # Pinia store definitions
├── types/           # TypeScript type definitions
├── App.vue          # Main application component
├── main.ts          # Application entry point
├── style.css        # Global styles
└── vite-env.d.ts    # Vite environment declarations
```

## Building and Running
The project uses npm scripts for common development tasks:

- `npm run dev` - Start the development server with hot module replacement
- `npm run build` - Compile TypeScript and build the project for production
- `npm run preview` - Preview the production build locally

## Development Conventions
1. **Pinia Stores**: 
   - Uses the Composition API syntax with `defineStore`
   - Stores are defined in the `src/stores` directory
   - State, getters, and actions are defined using Vue's Composition API (ref, computed)

2. **TypeScript**:
   - Strict mode enabled
   - Type definitions in `src/types` directory
   - Uses `.ts` extension for TypeScript files

3. **Component Structure**:
   - Uses Vue 3's `<script setup>` syntax
   - Components use TypeScript for props and emits

4. **State Management**:
   - Uses `storeToRefs` to maintain reactivity when destructuring store properties
   - Asynchronous operations are handled directly in store actions

## Key Implementation Details
1. **Store Implementation** (`src/stores/count.ts`):
   - Defines a counter store with both synchronous and asynchronous actions
   - Includes a counter value, computed double count, and API data fetching
   - Uses Axios for HTTP requests to fetch channel data

2. **Type Definitions** (`src/types/index.d.ts`):
   - Defines types for API response data structures
   - Strongly types the channel data returned from the API

3. **Main Application** (`src/App.vue`):
   - Consumes the Pinia store using `useCountStore`
   - Uses `storeToRefs` to maintain reactivity
   - Displays counter value, double count, and fetched channel data