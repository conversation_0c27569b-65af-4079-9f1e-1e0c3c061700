package com.hnguigu.springcloud.config;

import com.alibaba.cloud.sentinel.annotation.SentinelRestTemplate;
import com.hnguigu.springcloud.handler.HnguiguSentinelRestTemplateHandler;
import com.hnguigu.springcloud.interceptor.CustomRestTemplateInterceptor;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Bean
    @LoadBalanced

    /**
     * blockHandler:处理流控
     * fallback:处理非流控异常，走兜底方案（降级）
     */
    @SentinelRestTemplate(blockHandler = "handleBlock", blockHandlerClass =
            HnguiguSentinelRestTemplateHandler.class)
    public RestTemplate restTemplate(CustomRestTemplateInterceptor customRestTemplateInterceptor) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(customRestTemplateInterceptor);
        return restTemplate;
    }
}
