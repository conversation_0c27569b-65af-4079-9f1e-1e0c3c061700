package com.hnguigu.springcloud.controller;

import com.hnguigu.springcloud.entity.Order;
import com.hnguigu.springcloud.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/orders")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @PostMapping("/{goodsId}")
    public String createOrder(@RequestBody Order order, @PathVariable Long goodsId) {
        this.orderService.createOrder(order, goodsId);
        return "success";
    }
}
