package com.hnguigu.springcloud.service.impl;


import com.hnguigu.springcloud.entity.Goods;
import com.hnguigu.springcloud.entity.Order;
import com.hnguigu.springcloud.feign.GoodsServiceFeign;
import com.hnguigu.springcloud.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Autowired
    private GoodsServiceFeign goodsServiceFeign;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public void createOrder(Order order, Long goodsId) {
        if (ObjectUtils.isEmpty(order)) {
            throw new IllegalArgumentException("");
        }

        if (ObjectUtils.isEmpty(goodsId) || goodsId <= 0) {
            throw new IllegalArgumentException("商品id必须大于0");
        }

        Goods goods = this.goodsServiceFeign.findGoodsById(goodsId);

//        Goods goods = this.restTemplate.getForObject("http://goods-service/api/goods/" + goodsId, Goods.class);
        if (!ObjectUtils.isEmpty(goods)) {
            order.setGoods(goods);
        }

        log.info("创建订单:{}", order);

    }
}
