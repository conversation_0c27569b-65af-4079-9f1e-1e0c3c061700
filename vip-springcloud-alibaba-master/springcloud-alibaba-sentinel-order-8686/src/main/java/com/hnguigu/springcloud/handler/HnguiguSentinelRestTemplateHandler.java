package com.hnguigu.springcloud.handler;

import com.alibaba.cloud.sentinel.rest.SentinelClientHttpResponse;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import com.alibaba.fastjson.JSON;
import com.sun.prism.shader.Mask_TextureSuper_AlphaTest_Loader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.stereotype.Component;

/**
 * ClientHttpRequestInterceptor#intercept一致
 * 两个方法的最后的那个参数必须是BlockException
 */
@Slf4j
@Component
public class HnguiguSentinelRestTemplateHandler {

    public static SentinelClientHttpResponse handleBlock(HttpRequest request, byte[] body,
                                                         ClientHttpRequestExecution execution, BlockException ex) {
        String message = "限流了";
        if (ex instanceof FlowException) {
            FlowException flowException = (FlowException) ex;
            message = flowException.getMessage();
        } else if (ex instanceof DegradeException) {
            DegradeException degradeException = (DegradeException) ex;
            message = degradeException.getMessage();
        } else if (ex instanceof ParamFlowException) {
            ParamFlowException paramFlowException = (ParamFlowException) ex;
            message = paramFlowException.getMessage();
        }

        if (null == message) {
            message = "限流了";
        }

        // JSON格式的字符串
        message = "{\"code\": 429, \"message\": \"" + message + "\"}";
        SentinelClientHttpResponse response = new SentinelClientHttpResponse(message);
        return response;
    }

    /**
     *
     * @param request
     * @param body
     * @param execution
     * @param ex
     * @return
     */
    /*public static SentinelClientHttpResponse handleFallback(HttpRequest request, byte[] body,
                                                            ClientHttpRequestExecution execution, BlockException ex) {
        String message = "限流了";
        if (ex instanceof FlowException) {
            FlowException flowException = (FlowException) ex;
            message = flowException.getMessage();
        } else if (ex instanceof DegradeException) {
            DegradeException degradeException = (DegradeException) ex;
            message = degradeException.getMessage();
        } else if (ex instanceof ParamFlowException) {
            ParamFlowException paramFlowException = (ParamFlowException) ex;
            message = paramFlowException.getMessage();
        }

        SentinelClientHttpResponse response = new SentinelClientHttpResponse(message);
        return response;
    }*/

}
