package com.hnguigu.springcloud.interceptor;

import com.alibaba.cloud.sentinel.rest.SentinelClientHttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class CustomRestTemplateInterceptor implements ClientHttpRequestInterceptor {

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        try {
            return execution.execute(request, body);
        } catch (Exception e) {
            log.error("RestTemplate request failed: {}", e.getMessage());
            String message = "{\"code\": 500, \"message\": \"服务异常: " + e.getMessage() + "\"}";
            return new SentinelClientHttpResponse(message);
        }
    }
}
