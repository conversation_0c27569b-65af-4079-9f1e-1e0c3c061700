package com.hnguigu.springcloud.feign;

import com.hnguigu.springcloud.entity.Goods;
import org.springframework.stereotype.Component;

@Component
public class HnguiguOpenFeignSentinelFallbackHandler implements GoodsServiceFeign {

    @Override
    public Goods findGoodsById(Long id) {
        Goods goods = new Goods();
        goods.setId(-1L);
        goods.setName("兜底商品");
        goods.setPrice(0D);
        return goods;
    }
}
