package com.hnguigu.springcloud.feign;

import com.hnguigu.springcloud.entity.Goods;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class MyOpenFeignSentinelFallbackFactory implements FallbackFactory<GoodsServiceFeign> {

    @Override
    public GoodsServiceFeign create(Throwable cause) {
        return new GoodsServiceFeign() {
            @Override
            public Goods findGoodsById(Long id) {
                Goods goods = new Goods();
                goods.setId(-1L);
                goods.setName("兜底商品");
                goods.setPrice(0D);
                return goods;
            }
        };
    }
}
