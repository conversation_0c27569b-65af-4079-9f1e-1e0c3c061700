package com.hnguigu.springcloud.feign;

import com.hnguigu.springcloud.entity.Goods;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * @FeignClient的value属性：调用的是什么微服务的名称
 */
@FeignClient(value = "goods-service", path = "/api/goods",
//        fallback = HnguiguOpenFeignSentinelFallbackHandler.class,
        fallbackFactory = MyOpenFeignSentinelFallbackFactory.class)
public interface GoodsServiceFeign {

    @GetMapping("/{id}")
    public Goods findGoodsById(@PathVariable(value = "id") Long id);


    /*@RequestLine("GET /{id}")
    public Goods findGoodsById(@Param(value = "id") Long id);*/

}
