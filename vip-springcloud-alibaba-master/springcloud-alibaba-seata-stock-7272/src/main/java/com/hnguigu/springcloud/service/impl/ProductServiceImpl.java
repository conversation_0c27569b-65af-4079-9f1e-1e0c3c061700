package com.hnguigu.springcloud.service.impl;

import com.hnguigu.springcloud.entity.Product;
import com.hnguigu.springcloud.mapper.ProductMapper;
import com.hnguigu.springcloud.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

@Service
@Transactional
public class ProductServiceImpl implements ProductService {

    @Autowired
    private ProductMapper productMapper;

    @Override
    public void reduceCount(Integer productId, Integer count) {
        if (ObjectUtils.isEmpty(productId) || ObjectUtils.isEmpty(count)) {
            throw new IllegalArgumentException("");
        }

        if (productId <= 0 || count <= 0) {
            throw new IllegalArgumentException("");
        }

        Product product = this.productMapper.findByProductId(productId);
        if (!ObjectUtils.isEmpty(product)) {
            if (product.getCount() < count) {
                throw new RuntimeException("xxxxx");
            }

            product.setCount(product.getCount() - count);
            this.productMapper.update(product);

//            System.out.println(10 / 0);
        }
    }
}
