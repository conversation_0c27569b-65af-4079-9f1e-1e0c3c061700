package com.hnguigu.springcloud.controller;

import com.hnguigu.springcloud.service.ProductService;

import com.hnguigu.springcloud.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/product")
public class ProductController {


    @Autowired
    private ProductService productService;

    /**
     * 减库存
     *
     * @param productId
     * @param count
     * @return
     */
    @RequestMapping("/reduceCount")
    public Result reduceCount(Integer productId, Integer count) {
        Result result = new Result();
        this.productService.reduceCount(productId, count);
        result.setSuccess(true);
        result.setMsg("扣除库存成功");
        result.setData("扣除库存成功");
        return result;

        /*try {

            this.productService.reduceCount(productId, count);
            result.setSuccess(true);
            result.setMsg("扣除库存成功");
            result.setData("扣除库存成功");

        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setMsg("扣除库存失败");
            result.setData("扣除库存失败");
        }*/

    }
}
