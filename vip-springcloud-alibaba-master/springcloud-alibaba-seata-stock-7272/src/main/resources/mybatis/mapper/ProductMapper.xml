<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hnguigu.springcloud.mapper.ProductMapper">

    <update id="update">
        UPDATE
            PRODUCT
        SET PRODUCT_ID = #{productId},
            PRICE      = #{price},
            COUNT      = #{count}
        WHERE ID = #{id}
    </update>

    <select id="findByProductId" resultType="com.hnguigu.springcloud.entity.Product">
        SELECT ID,
               PRODUCT_ID AS "productId",
               PRICE,
               COUNT
        FROM PRODUCT
        WHERE PRODUCT_ID = #{productId}
    </select>
</mapper>