server:
  port: 8887
spring:
  application:
    name: order-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848

logging:
  level:
    com.hnguigu.springcloud: debug

feign:
  okhttp:
    enabled: true
  httpclient:
    enabled: false    # 切换底层实现为httpclient
  client:
    config:
      goods-service:
        loggerLevel: full
        request-interceptors:
          - com.hnguigu.springcloud.interceptor.AuthenticateRequestInterceptor
#        connect-timeout: 3000
#        read-timeout: 5000

