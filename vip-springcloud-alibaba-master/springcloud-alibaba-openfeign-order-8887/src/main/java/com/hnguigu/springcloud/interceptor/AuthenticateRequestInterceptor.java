package com.hnguigu.springcloud.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

import java.util.UUID;

public class AuthenticateRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        String token = "Bearer " + UUID.randomUUID().toString();
        template.header("Authorization", token);
    }
}
