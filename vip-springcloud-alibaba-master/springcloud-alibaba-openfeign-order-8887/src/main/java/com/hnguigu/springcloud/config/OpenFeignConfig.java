package com.hnguigu.springcloud.config;

import com.hnguigu.springcloud.interceptor.AuthenticateRequestInterceptor;
import feign.Logger;
import feign.Request;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@EnableFeignClients(basePackages = "com.hnguigu.springcloud.feign")
public class OpenFeignConfig {

    /**
     * OpenFeign日志级别为：FULL
     *
     * @return
     */
//    @Bean
    public Logger.Level logLevel() {
        return Logger.Level.FULL;
    }

    /**
     * 设置了连接超时时间和读取超时时间
     *
     * @return
     */
//    @Bean
    public Request.Options options() {
        return new Request.Options(3000, 5000);
    }

    /**
     * 全局配置（任何的Feign调用都会走这个拦截器）
     * @return
     */
    /*@Bean
    public AuthenticateRequestInterceptor authenticateRequestInterceptor() {
        return new AuthenticateRequestInterceptor();
    }*/

    /**
     * 默认契约
     * @return
     */
   /* @Bean
    public Contract contract() {
        return new feign.Contract.Default();
    }*/
}
