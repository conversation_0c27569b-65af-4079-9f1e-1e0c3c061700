package com.hnguigu.springcloud;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

import java.util.concurrent.TimeUnit;

@SpringBootApplication
public class Config7777Application {

    public static void main(String[] args) {
        ConfigurableApplicationContext context =
                SpringApplication.run(Config7777Application.class, args);
        ConfigurableEnvironment environment = context.getEnvironment();

        while (true) {
            String username = environment.getProperty("user.username");
            String age = environment.getProperty("user.age");
            String salary = environment.getProperty("user.salary");
            String birthday = environment.getProperty("user.birthday");
            String description = environment.getProperty("user.description");
            String gender = environment.getProperty("user.gender");
            String sex = environment.getProperty("user.sex");
            System.out.println(username + "--------" + age + "-------" + salary + "--------"
                    + description + "-------" + birthday + "----" + gender + "----" + sex);

            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}
