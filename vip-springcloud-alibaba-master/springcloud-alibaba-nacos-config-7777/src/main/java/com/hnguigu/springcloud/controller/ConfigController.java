package com.hnguigu.springcloud.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/configs")
@Slf4j

// 支持动态刷新
@RefreshScope
public class ConfigController {

    @Value("${user.username}")
    private String username;

    @Value("${user.age}")
    private Integer age;

    @GetMapping
    public String getConfig() {
        log.info("username:{}", username);
        log.info("age:{}", age);
        return "success";
    }
}
