server:
  port: 7777
spring:
  application:
    name: config-service
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        file-extension: yaml      #配置项dataId的名称必须为${spring.application.name}.${spring.cloud.nacos.config.file-extension}
        namespace: efb5478e-c8a8-42b0-8e02-32c49ef965ef
        group: DEFAULT_GROUP
        shared-configs:      #共享配置（不是必须）。多个微服务里面如果有相同的配置，就可以做成共享配置
          - data-id: common1.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: common2.yaml
            group: DEFAULT_GROUP
            refresh: true
        extension-configs:
          - data-id: extension1.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: extension2.yaml
            group: DEFAULT_GROUP
            refresh: true
  profiles:
    active: dev                  #配置项dataId的名称必须为${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
#        name: config.yaml
