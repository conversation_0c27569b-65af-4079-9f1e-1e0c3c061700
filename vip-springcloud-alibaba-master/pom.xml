<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version>
    </parent>

    <groupId>com.hnguigu</groupId>
    <artifactId>vip-springcloud-alibaba-master</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>springcloud-alibaba-nacos-order-8888</module>
        <module>springcloud-alibaba-nacos-goods-9999</module>
        <module>springcloud-alibaba-api</module>
        <module>springcloud-alibaba-nacos-goods-9998</module>
        <module>springcloud-alibaba-nacos-config-7777</module>
        <module>springcloud-alibaba-openfeign-order-8887</module>
        <module>springcloud-alibaba-sentinel-order-8686</module>
        <module>springcloud-alibaba-seata-order-8282</module>
        <module>springcloud-alibaba-seata-stock-7272</module>
        <module>springcloud-alibaba-seata-account-6262</module>
        <module>springcloud-gateway-4444</module>
        <module>springcloud-alibaba-dubbo-producer-6666</module>
        <module>springcloud-alibaba-dubbo-consumer-7777</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Hoxton.SR9</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2.2.6.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.38</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
