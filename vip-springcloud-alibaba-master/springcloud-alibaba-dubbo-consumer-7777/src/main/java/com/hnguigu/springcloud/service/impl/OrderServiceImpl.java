package com.hnguigu.springcloud.service.impl;

import com.hnguigu.springcloud.entity.Goods;
import com.hnguigu.springcloud.entity.Order;
import com.hnguigu.springcloud.service.GoodsService;
import com.hnguigu.springcloud.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.ObjectUtils;

@DubboService
@Slf4j
public class OrderServiceImpl implements OrderService {

    private GoodsService goodsService;

    @DubboReference
    public void setGoodsService(GoodsService goodsService) {
        this.goodsService = goodsService;
    }

    @Override
    public void createOrder(Order order, Long goodsId) {
        if (ObjectUtils.isEmpty(order)) {
            throw new IllegalArgumentException("");
        }

        if (ObjectUtils.isEmpty(goodsId) || goodsId <= 0) {
            throw new IllegalArgumentException("商品id必须大于0");
        }

        // 服务的调用
        Goods goods = this.goodsService.findGoodsById(goodsId);
        if (!ObjectUtils.isEmpty(goods)) {
            order.setGoods(goods);
        }


        log.info("创建订单:{}", order);

    }
}
