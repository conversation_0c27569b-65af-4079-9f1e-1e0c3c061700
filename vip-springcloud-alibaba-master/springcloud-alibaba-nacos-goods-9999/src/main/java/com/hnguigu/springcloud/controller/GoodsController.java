package com.hnguigu.springcloud.controller;

import com.hnguigu.springcloud.entity.Goods;
import com.hnguigu.springcloud.service.GoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/goods")
public class GoodsController {

    @Autowired
    private GoodsService goodsService;

    @GetMapping("/{id}")
    public Goods findGoodsById(@PathVariable Long id) {
        return this.goodsService.findGoodsById(id);
    }

    /*@GetMapping("/{id}")
    public Goods findGoodsById(@PathVariable Long id, @RequestHeader("Authorization") String token) {
        log.info("authorization:{}", token);
        return this.goodsService.findGoodsById(id);
    }*/
}
