package com.hnguigu.springcloud.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 只能处理项目中抛出的关于流控异常的异常处理
 */
//@RestControllerAdvice
@Slf4j
public class MyExceptionHandler1 {

    @ExceptionHandler(value = ArithmeticException.class)
    public String handleException(Exception ex) {
        log.info("ArithmeticException");
        return ex.getMessage();
    }
}
