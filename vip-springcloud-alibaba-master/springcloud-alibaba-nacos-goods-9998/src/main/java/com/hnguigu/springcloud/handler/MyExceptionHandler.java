package com.hnguigu.springcloud.handler;

import com.alibaba.csp.sentinel.adapter.spring.webmvc.callback.BlockExceptionHandler;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import com.alibaba.csp.sentinel.slots.system.SystemBlockException;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 只能处理项目中抛出的关于流控异常的异常处理
 */
//@Component
public class MyExceptionHandler implements BlockExceptionHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
                       BlockException e) throws Exception {
        if (!ObjectUtils.isEmpty(e)) {
            if (e instanceof FlowException) {
                response.getWriter().write("限流了");
            } else if (e instanceof DegradeException) {
                response.getWriter().write("降级了");
            } else if (e instanceof ParamFlowException) {
                response.getWriter().write("热点参数限流");
            } else if (e instanceof SystemBlockException) {
                response.getWriter().write("系统规则（负载/...不满足要求）");
            } else if (e instanceof AuthorityException) {
                response.getWriter().write("授权失败");
            }
        }
    }
}
