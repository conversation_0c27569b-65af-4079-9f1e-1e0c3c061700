package com.hnguigu.springcloud.handler;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.hnguigu.springcloud.entity.Goods;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HnguiguBlockHandler {

    /**
     * 一旦出现流控，则交给该方法来处理
     * 该方法的签名有讲究：原始方法签名一致，而且参数列表在最后加上BlockException
     *
     * @param id
     * @param e
     * @return
     */
    public static Goods handleBlock(Long id, BlockException e) {
        Goods goods = new Goods();
        goods.setId(-1L);
        goods.setName("商品" + id + "被限流了");
        goods.setPrice(0D);
        return goods;
    }

    public static Goods handleFallback(Long id, Throwable e) {
        log.info("fallback:{}", e.getMessage());
        Goods goods = new Goods();
        goods.setId(-1L);
        goods.setName("商品" + id + "出错了");
        goods.setPrice(0D);
        return goods;
    }
}
