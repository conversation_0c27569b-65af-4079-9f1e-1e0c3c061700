package com.hnguigu.springcloud.controller;

import com.hnguigu.springcloud.entity.Goods;
import com.hnguigu.springcloud.service.GoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/goods")
@Slf4j
public class GoodsController {

    @Autowired
    private GoodsService goodsService;

    /*@GetMapping("/{id}")
    public Goods findGoodsById(@PathVariable Long id) {
//        int i = 10 / 0;
        return this.goodsService.findGoodsById(id);
    }*/

    @GetMapping("/{id}")
    public Goods findGoodsById(@PathVariable Long id, @RequestHeader("Authorization") String token) {
        log.info("authorization:{}", token);
        return this.goodsService.findGoodsById(id);
    }

    /**
     * 热点参数规则的测试
     * 当id==1000这个商品时，这个商品是热点商品，因此我们要流控
     * 当id!=1000这个商品时，这个商品不是热点商品，因此我们不流控
     * <p>
     * <p>
     * blockHandler 处理流控异常 BlockException
     *
     * @param id
     * @return
     */
    @GetMapping("/param/{id}")
    /*@SentinelResource(value = "getById",
            blockHandler = "handleBlock",
            blockHandlerClass = HnguiguBlockHandler.class,
            fallback = "handleFallback",
            fallbackClass = HnguiguBlockHandler.class)*/ public Goods getById(@PathVariable Long id) {
        int i = 10 / 0;
        return this.goodsService.findGoodsById(id);
    }

    /**
     * 一旦出现流控，则交给该方法来处理
     * 该方法的签名有讲究：原始方法签名一致，而且参数列表在最后加上BlockException
     *
     * @param id
     * @param e
     * @return
     *//*
    public Goods handleBlock(Long id, BlockException e) {
        Goods goods = new Goods();
        goods.setId(-1L);
        goods.setName("商品" + id + "被限流了");
        goods.setPrice(0D);
        return goods;
    }*/

    /*@GetMapping("/{id}")
    public Goods findGoodsById(@PathVariable Long id,
                               @RequestHeader("Authorization") String token) {
        log.info("token:{}", token);
        return this.goodsService.findGoodsById(id);
    }*/
}
