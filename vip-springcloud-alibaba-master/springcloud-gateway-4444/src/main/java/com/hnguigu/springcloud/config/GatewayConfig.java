package com.hnguigu.springcloud.config;

import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.cors.reactive.CorsUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

@Configuration
public class GatewayConfig {

    @Bean
    public KeyResolver keyResolver() {
        // URL路径限流
        return exchange -> Mono.just(exchange.getRequest().getURI().getPath());
    }

    /**
     * 跨域过滤器配置
     * 注意：如果使用了 application.yml 中的 globalcors 配置，这个 Bean 可以注释掉
     */
    @Bean
    public WebFilter corsFilter() {
        return (ServerWebExchange ctx, WebFilterChain chain) -> {
            ServerHttpRequest request = ctx.getRequest();
            if (CorsUtils.isCorsRequest(request)) {
                ServerHttpResponse response = ctx.getResponse();
                HttpHeaders headers = response.getHeaders();

                // 允许的源（根据实际需求修改）
                headers.add("Access-Control-Allow-Origin", "*");
                // 如果需要携带认证信息，需要指定具体域名，不能使用 "*"
                // headers.add("Access-Control-Allow-Origin", "http://localhost:3000");

                // 允许的方法
                headers.add("Access-Control-Allow-Methods", "GET, PUT, POST, DELETE, OPTIONS");

                // 允许的请求头
                headers.add("Access-Control-Allow-Headers", "*");

                // 是否允许携带认证信息
                headers.add("Access-Control-Allow-Credentials", "true");

                // 预检请求缓存时间
                headers.add("Access-Control-Max-Age", "3600");

                // 处理预检请求
                if (request.getMethod() == HttpMethod.OPTIONS) {
                    response.setStatusCode(HttpStatus.OK);
                    return Mono.empty();
                }
            }
            return chain.filter(ctx);
        };
    }
}
