package com.hnguigu.springcloud.predicate;

import org.springframework.cloud.gateway.handler.predicate.AbstractRoutePredicateFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.server.ServerWebExchange;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;

@Component
public class ParameterRoutePredicateFactory extends AbstractRoutePredicateFactory<ParameterRoutePredicateFactory.Config> {

    public static final String PARAMETER_KEY = "parameter";

    public ParameterRoutePredicateFactory() {
        super(ParameterRoutePredicateFactory.Config.class);
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList(PARAMETER_KEY);
    }

    @Override
    public Predicate<ServerWebExchange> apply(Config config) {

        // 配置文件中的参数名称 parameterName为“info”
        String parameterName = config.getParameter();

        return new Predicate<ServerWebExchange>() {
            @Override
            public boolean test(ServerWebExchange serverWebExchange) {
                MultiValueMap<String, String> queryParams = serverWebExchange.getRequest().getQueryParams();
                List<String> parameterValueList = queryParams.get(parameterName);
                if (!ObjectUtils.isEmpty(parameterValueList)) {
                    return true;
                }
                return false;
            }
        };
    }

    @Validated
    public static class Config {

        @NotEmpty
        private String parameter;

        public String getParameter() {
            return parameter;
        }

        public void setParameter(String parameter) {
            this.parameter = parameter;
        }
    }
}
