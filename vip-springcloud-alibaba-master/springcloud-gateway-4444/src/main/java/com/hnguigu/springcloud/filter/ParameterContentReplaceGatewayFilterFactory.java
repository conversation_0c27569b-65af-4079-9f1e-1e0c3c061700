package com.hnguigu.springcloud.filter;

import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

@Component
public class ParameterContentReplaceGatewayFilterFactory extends AbstractGatewayFilterFactory<ParameterContentReplaceGatewayFilterFactory.Config> {

    public static final String PARAMETER_NAME_KEY = "parameterName";
    public static final String PARAMETER_VALUE_KEY = "parameterValue";

    public ParameterContentReplaceGatewayFilterFactory() {
        super(ParameterContentReplaceGatewayFilterFactory.Config.class);
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList(PARAMETER_NAME_KEY, PARAMETER_VALUE_KEY);
    }

    @Override
    public GatewayFilter apply(Config config) {
        String parameterName = config.getParameterName();
        String parameterValue = config.getParameterValue(); // helloworld

        return new GatewayFilter() {
            @Override
            public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
                List<String> parameterValueList = exchange.getRequest().getQueryParams().get(parameterName);

                if (!CollectionUtils.isEmpty(parameterValueList)) {
                    String oldParameterValue = parameterValueList.get(0);
                    String newParameterValue = oldParameterValue.replaceAll("java", parameterValue);

                    // 将新的参数值设置到请求中
                    exchange.getRequest().mutate().header("Authorization", newParameterValue).build();
                }

                return chain.filter(exchange);
            }
        };
    }

    public static class Config {

        private String parameterName;
        private String parameterValue;

        public String getParameterName() {
            return parameterName;
        }

        public void setParameterName(String parameterName) {
            this.parameterName = parameterName;
        }

        public String getParameterValue() {
            return parameterValue;
        }

        public void setParameterValue(String parameterValue) {
            this.parameterValue = parameterValue;
        }
    }


}
