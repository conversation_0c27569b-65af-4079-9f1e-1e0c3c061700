package com.hnguigu.springcloud.service.impl;

import com.hnguigu.springcloud.entity.Goods;
import com.hnguigu.springcloud.service.GoodsService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.ObjectUtils;

import java.util.concurrent.TimeUnit;

@DubboService
public class GoodsServiceImpl implements GoodsService {

    @Override
    public Goods findGoodsById(Long id) {
        if (ObjectUtils.isEmpty(id) || id <= 0) {
            throw new IllegalArgumentException("id必须大于0");
        }

        Goods goods = new Goods();
        goods.setId(id);
        goods.setName("商品" + id);
        goods.setPrice(200D);
        return goods;
    }
}
