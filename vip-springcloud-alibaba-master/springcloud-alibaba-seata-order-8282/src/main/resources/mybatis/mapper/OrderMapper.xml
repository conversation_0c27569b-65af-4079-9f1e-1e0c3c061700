<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hnguigu.springcloud.mapper.OrderMapper">
    <insert id="save" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO `order` (USER_ID, PAY_MONEY, PRODUCT_ID, STATUS, `count`)
        VALUES (#{userId}, #{payMoney}, #{productId}, #{status}, #{count})
    </insert>

    <update id="update">
        UPDATE
            `order`
        SET USER_ID    = #{userId},
            PAY_MONEY  = #{payMoney},
            PRODUCT_ID = #{productId},
            STATUS     = #{status},
            COUNT      = #{count}
        WHERE ID = #{id}
    </update>

    <select id="findById" resultType="com.hnguigu.springcloud.entity.Order">
        SELECT ID,
               USER_ID    AS "userId",
               PAY_MONEY  AS "payMoney",
               PRODUCT_ID AS "productId",
               STATUS,
               COUNT
        FROM `order`
        WHERE ID = #{id}
    </select>
</mapper>