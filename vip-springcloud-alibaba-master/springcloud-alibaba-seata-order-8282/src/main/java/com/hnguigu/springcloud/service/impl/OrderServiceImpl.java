package com.hnguigu.springcloud.service.impl;

import com.hnguigu.springcloud.entity.Order;
import com.hnguigu.springcloud.feign.AccountFeignClient;
import com.hnguigu.springcloud.feign.ProductFeignClient;
import com.hnguigu.springcloud.mapper.OrderMapper;
import com.hnguigu.springcloud.service.OrderService;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
// 此环境下是本地事务，因此不要再写这个注解
//@Transactional
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private AccountFeignClient accountFeignClient;

    @Autowired
    private ProductFeignClient productFeignClient;

    @Override
    @GlobalTransactional(name = "createOrder")
    public void createOrder(Order order) {
        if (ObjectUtils.isEmpty(order)) {
            throw new IllegalArgumentException("订单信息有误");
        }

        // 保存订单
        this.orderMapper.save(order);

        // 扣减库存
        this.productFeignClient.reduceCount(order.getCount(), order.getProductId());

        // 扣减余额
        this.accountFeignClient.reduceBalance(order.getUserId(), order.getPayMoney());

        this.modifyOrderStatus(order.getId(), 1);

//        System.out.println(10 / 0);

    }

    @Override
    public void modifyOrderStatus(Integer id, int status) {
        if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(status)) {
            throw new IllegalArgumentException("");
        }

        Order order = this.orderMapper.findById(id);
        order.setStatus(status);
        this.orderMapper.update(order);
    }
}
