package com.hnguigu.springcloud.feign;

import com.hnguigu.springcloud.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@FeignClient(value = "account-service", path = "/account")
public interface AccountFeignClient {

    @RequestMapping("/reduceBalance")
    @ResponseBody
    public Result reduceBalance(@RequestParam("userId") Integer userId,
                                @RequestParam("money") Float money);

}
