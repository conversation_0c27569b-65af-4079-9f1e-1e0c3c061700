package com.hnguigu.springcloud.feign;

import com.hnguigu.springcloud.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@FeignClient(value = "stock-service", path = "/product")
public interface ProductFeignClient {

    @RequestMapping("/reduceCount")
    @ResponseBody
    public Result reduceCount(@RequestParam("productId") Integer productId,
                              @RequestParam("count") Integer count);
}
