package com.hnguigu.springcloud.controller;

import com.hnguigu.springcloud.entity.Order;
import com.hnguigu.springcloud.service.OrderService;
import com.hnguigu.springcloud.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @PostMapping("/createOrder")
    public Result createOrder(@RequestBody Order order) {
        Result result = new Result();
        /*try {
            this.orderService.createOrder(order);

            result.setSuccess(true);
            result.setMsg("创建订单成功");
            result.setData("创建订单成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setMsg("创建订单失败");
            result.setData("创建订单失败");
        }*/

        this.orderService.createOrder(order);
        result.setSuccess(true);
        result.setMsg("创建订单成功");
        result.setData("创建订单成功");
        return result;
    }
}

