package com.hnguigu.springcloud.controller;

import com.hnguigu.springcloud.service.AccountService;
import com.hnguigu.springcloud.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/account")
public class AccountController {

    @Autowired
    private AccountService accountService;

    @RequestMapping("/reduceBalance")
    public Result reduceBalance(Integer userId, Float money) {
        Result result = new Result();
        this.accountService.reduceBalance(userId, money);
        result.setSuccess(true);
        result.setMsg("扣款成功");
        result.setData("扣款成功");
        
        return result;
        /*try {
            this.accountService.reduceBalance(userId, money);

            result.setSuccess(true);
            result.setMsg("扣款成功");
            result.setData("扣款成功");
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setMsg("扣款失败");
            result.setData("扣款失败");
        }*/

    }
}
