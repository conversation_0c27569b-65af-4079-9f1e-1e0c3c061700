package com.hnguigu.springcloud.service.impl;

import com.hnguigu.springcloud.entity.Account;
import com.hnguigu.springcloud.mapper.AccountMapper;
import com.hnguigu.springcloud.service.AccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

@Service
@Transactional
public class AccountServiceImpl implements AccountService {

    @Autowired
    private AccountMapper accountMapper;

    @Override
    public void reduceBalance(Integer userId, Float balance) {
        if (ObjectUtils.isEmpty(userId) || ObjectUtils.isEmpty(balance)) {
            throw new IllegalArgumentException("");
        }

        if (userId <= 0 || balance.longValue() <= 0) {
            throw new IllegalArgumentException("");
        }

        Account account = this.accountMapper.findByUserId(userId);
        if (!ObjectUtils.isEmpty(account)) {
            if (account.getBalance() < balance) {
                throw new RuntimeException("余额不足");
            }

            account.setBalance(account.getBalance() - balance);
            this.accountMapper.update(account);

            System.out.println(10 / 0);

        }
    }
}
