server:
  port: 6262

spring:
  application:
    name: account-service
  main:
    allow-bean-definition-overriding: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari: # springboot内置的数据源（光）
      driver-class-name: com.mysql.jdbc.Driver
      username: root
      password: root
      jdbc-url: ***************************************************************************************************************************
      minimum-idle: 5
      maximum-pool-size: 30
      connection-timeout: 30000
      max-lifetime: 1800000
      idle-timeout: 60000
      auto-commit: true
      pool-name: DatebookHikariCP
  cloud:
    nacos:
      server-addr: 127.0.0.1:8848

    alibaba:
      seata:
        tx-service-group: my_test_tx_group   #事务分组的名称 该名称与config.txt文件中的service.vgroupMapping.my_test_tx_group=default一致
