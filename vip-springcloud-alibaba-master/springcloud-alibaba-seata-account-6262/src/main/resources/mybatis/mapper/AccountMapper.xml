<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hnguigu.springcloud.mapper.AccountMapper">

    <update id="update">
        UPDATE
            ACCOUNT
        SET USER_ID = #{userId},
            BALANCE = #{balance}
        WHERE ID = #{id};
    </update>

    <select id="findByUserId" resultType="com.hnguigu.springcloud.entity.Account">
        SELECT ID,
               USER_ID AS "userId",
               BALANCE
        FROM ACCOUNT
        WHERE USER_ID = #{userId}
    </select>
</mapper>
