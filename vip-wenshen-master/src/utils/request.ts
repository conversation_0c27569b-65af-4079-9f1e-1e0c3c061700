/*
 * Axios的封装
 *
 * 1.创建对象
 *   base
 *   timeout
 *
 * 2.拦截器
 *   请求拦截
 *   响应拦截
 * */

// 创建axios实例
import axios, { type Method } from 'axios'
import { useUserStore } from '@/stores'
import { showToast } from 'vant'
import router from '@/router'

const instance = axios.create({
  baseURL: 'https://consult-api.itheima.net/',
  timeout: 10000,
})

/*// 配置拦截器
// 请求拦截器
request.interceptors.request.use(
  function (config) {
    // 2. 添加头信息
    config.headers['Authorization'] = 'Bearer ' + localStorage.getItem('token')
    return config
  },
  function (error) {
    return Promise.reject(error)
  },
)

// 响应拦截器
request.interceptors.response.use(
  function (response) {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    // 3. 状态码如果是2xx,属于成功的响应。处理数据。返回数据
    if (response.status === 200) {
      return response.data
    }
    return response
  },
  function (error) {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Do something with response error
    // 4. 状态码不是2xx,属于失败的响应。处理错误。返回错误

    return Promise.reject(error)
  },
)*/

// 2. 请求拦截器，携带token
instance.interceptors.request.use(
  (config) => {
    // 使用Pinia
    const userStore = useUserStore()
    if (userStore.user?.token && config.headers) {
      // 添加头信息（headerName:headerValue）
      // 模板字符串
      config.headers['Authorization'] = `Bearer ${userStore.user?.token}`
    }
    return config
  },
  (err) => Promise.reject(err),
)

// 3. 响应拦截器，剥离无效数据，401拦截
instance.interceptors.response.use(
  (res) => {
    // 后台约定，响应成功，但是code不是10000(后端约定)，是业务逻辑失败
    if (res.data?.code !== 10000) {
      showToast(res.data?.message || '业务失败')
      return Promise.reject(res.data)
    }
    // 业务逻辑成功，返回响应数据，作为axios成功的结果
    return res.data
  },
  (err) => {
    if (err.response.status === 401) {
      // 删除用户信息
      const store = useUserStore()
      store.deleteUser()
      // 跳转登录，带上接口失效所在页面的地址，登录完成后回跳使用
      router.push({
        path: '/login',
        query: { returnUrl: router.currentRoute.value.fullPath },
      })
    }
    return Promise.reject(err)
  },
)

// 4. 请求工具函数
/**
 *
 * @param url
 * @param method
 * @param submitData 参数
 */
const request = (url: string, method: Method = 'GET', submitData?: object) => {
  return instance.request({
    url,
    method,
    [method.toUpperCase() === 'GET' ? 'params' : 'data']: submitData
  })
}


export default request
