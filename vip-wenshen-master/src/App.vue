<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import HelloWorld from './components/HelloWorld.vue'
// import { useUserStore } from '@/stores/modules/user.ts'
import { useCounterStore, useUserStore } from './stores'
import { storeToRefs } from 'pinia'
import { onMounted } from 'vue'
import { request } from 'axios'
import { getUserList } from '@/services/user.ts'

const userStore = useUserStore()
const { user } = storeToRefs(userStore)

const countStore = useCounterStore()
const { count } = storeToRefs(countStore)

const handleModifyUser = () => {
  userStore.setUser({
    token: '123456',
    id: '2000',
    account: '********',
    mobile: '********',
    avatar: 'ABCD',
  })
}

const handleDeleteUser = () => {
  userStore.deleteUser()
}

onMounted(() => {
  getInitData()
})

const getInitData = async () => {
}


</script>

<template>
  <header>
    <img alt="Vue logo" class="logo" src="@/assets/logo.svg" width="125" height="125" />

    <div class="wrapper">
      <HelloWorld msg="You did it!" />

      <nav>
        <RouterLink to="/">Home</RouterLink>
        <RouterLink to="/about">About</RouterLink>
      </nav>
    </div>
  </header>

  <van-button type="primary">Primary</van-button>
  <van-button type="success">Success</van-button>
  <van-button type="default">Default</van-button>
  <van-button type="danger">Danger</van-button>
  <van-button type="warning">Warning</van-button>

  <van-form @submit="onSubmit">
    <van-cell-group inset>
      <van-field
        v-model="username"
        name="username"
        label="Username"
        placeholder="Username"
        :rules="[{ required: true, message: 'Username is required' }]"
      />
      <van-field
        v-model="password"
        type="password"
        name="password"
        label="Password"
        placeholder="Password"
        :rules="[{ required: true, message: 'Password is required' }]"
      />
    </van-cell-group>
    <div style="margin: 16px">
      <van-button round block type="primary" native-type="submit"> Submit </van-button>
    </div>
  </van-form>

  <a href="http://www.google.com">链接</a>

  <p>{{ user }}</p>
  <button @click="handleModifyUser">修改用户</button>
  <button @click="handleDeleteUser">删除用户</button>

  <p>{{ count }}</p>

  <!--
     #########################  开始测试了 #########################################
  -->






















































  <!--
     #########################  TODO 开始测试了
     #########################################
  -->




  <RouterView />
</template>

<style scoped>
header {
  line-height: 1.5;
  max-height: 100vh;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

nav {
  width: 100%;
  font-size: 12px;
  text-align: center;
  margin-top: 2rem;
}

nav a.router-link-exact-active {
  color: var(--color-text);
}

nav a.router-link-exact-active:hover {
  background-color: transparent;
}

nav a {
  display: inline-block;
  padding: 0 1rem;
  border-left: 1px solid var(--color-border);
}

nav a:first-of-type {
  border: 0;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }

  nav {
    text-align: left;
    margin-left: -1rem;
    font-size: 1rem;

    padding: 1rem 0;
    margin-top: 1rem;
  }
}

a {
  color: var(--cp-primary);
}
</style>
