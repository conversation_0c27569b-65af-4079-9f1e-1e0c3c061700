import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore(
  'user',
  () => {
    // 定义state
    const user = ref<User | null>({
      token: 'fhyskhfksdhfkjsdfsef',
      id: '1000',
      account: '********',
      mobile: '********',
      avatar: 'abcd',
    })

    // 定义action

    // TODO 1.修改用户
    const setUser = (newUser: User) => {
      console.log('修改用户')
      user.value = newUser
    }

    // TODO 2.删除用户
    const deleteUser = () => {
      console.log('删除用户')
      if (user.value) {
        user.value = null
      }
    }

    // 返回state和action
    return {
      user,
      setUser,
      deleteUser,
    }
  },
  {
    persist: true,
  },
)
