/*const arr1: number[] = [1, 2, 3, 4, 5]
arr1.forEach(item => console.log(item))

const arr2: Array<number> = [1, 2, 3, 4, 5]
arr2.forEach(item => console.log(item))

const arr3: (number | string)[] = [1, 2, 3, 'a', 'b']
arr3.forEach(item => console.log(item))

type ItemType = (number | string)[]
const arr4: ItemType = [1, 2, 3, 'a', 'b']
arr4.forEach(item => console.log(item))

const addFun = (a: number, b: number): number => {
    return a + b
}

let result = addFun(1, 2);
console.log(result)

type FunType = (a: number, b: number) => number
const addFun1: FunType = (a, b) => {
    return a + b
}

const result1 = addFun1(1, 2)
console.log(result1)*/


/*
* 编写一个arr2Str函数，作用为把数组转换为字符串，其中数组中既可以包含字符串和数字，分隔符也可
* 以进行自定义，类型为字符串类型，使用样例：
* 1. arr2Str( [1, 2, 3] , '-' ) -> '1-2-3'   join
* 2. arr2Str( [‘4’, ’5’] , ’&’ ) -> '4&5'
* */

/*const arr2Str = (sourceArray: (number | string)[], split: string): string => {
    const defaultSplit = ','

    if (sourceArray == null || sourceArray.length === 0) {
        return ''
    }

    if (split == null || split === '') {
        split = defaultSplit
    }

    return sourceArray.join(split);
}

const result3 = arr2Str([1, 2, 3, 'Helloworld'], '-')
console.log(result3)*/





































