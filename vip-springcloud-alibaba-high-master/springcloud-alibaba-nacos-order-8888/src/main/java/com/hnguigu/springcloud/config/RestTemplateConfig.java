package com.hnguigu.springcloud.config;

import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 * 使用Spring Cloud LoadBalancer进行负载均衡
 */
@Configuration
//@LoadBalancerClient(name = "goods-service", configuration = LoadBalancerConfig.class)
public class RestTemplateConfig {

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    // 如果需要使用Ribbon，可以取消注释以下配置
    /*
    @RibbonClients(
            value = {
                    @RibbonClient(name = "goods-service", configuration = RibbonConfig.class)
            }
    )
    */
}
