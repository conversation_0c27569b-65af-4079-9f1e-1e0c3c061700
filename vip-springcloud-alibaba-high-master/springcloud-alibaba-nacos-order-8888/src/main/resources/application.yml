server:
  port: 8888
spring:
  application:
    name: order-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    loadbalancer:
      enabled: true
      ribbon:
        enabled: false    #不开启Ribbon。而是使用LoadBalancer组件来实现负载均衡

## 负载均衡算法配置
## 可选值: random, round-robin, weighted-response-time, best-available, availability-filtering, custom
#loadbalancer:
#  algorithm: round-robin
#
## Spring Cloud LoadBalancer配置
##goods-service:
##  ribbon:
##    NFLoadBalancerRuleClassName: com.netflix.loadbalancer.RandomRule
#
## 日志配置，用于观察负载均衡效果
#logging:
#  level:
#    com.hnguigu.springcloud: DEBUG
#    org.springframework.cloud.loadbalancer: DEBUG
