# LoadBalancer组件负载均衡算法切换指南

## 概述

本项目演示了如何在Spring Cloud Alibaba项目中使用LoadBalancer组件实现负载均衡算法的切换。支持多种负载均衡策略，包括轮询、随机、自定义权重算法等。

## 项目架构

- **order-service (8888)**: 订单服务，作为消费者
- **goods-service (9998, 9999)**: 商品服务，作为提供者，运行两个实例

## 负载均衡实现方式

### 1. Spring Cloud LoadBalancer（推荐）

项目已配置使用Spring Cloud LoadBalancer替代Ribbon：

```yaml
spring:
  cloud:
    loadbalancer:
      enabled: true
      ribbon:
        enabled: false
```

### 2. 支持的负载均衡算法

#### 内置算法：
- **round-robin**: 轮询算法（默认）
- **random**: 随机算法
- **custom**: 自定义权重算法

#### 配置方式：
在 `application.yml` 中配置：
```yaml
loadbalancer:
  algorithm: round-robin  # 可选: round-robin, random, custom
```

## 使用方法

### 1. 启动服务

1. 启动Nacos服务器（127.0.0.1:8848）
2. 启动商品服务实例：
   - `NacosGoods9998Application` (端口9998)
   - `NacosGoods9999Application` (端口9999)
3. 启动订单服务：
   - `NacosOrder8888Application` (端口8888)

### 2. 切换负载均衡算法

#### 方法一：修改配置文件
修改 `application.yml` 中的 `loadbalancer.algorithm` 配置，然后重启应用。

#### 方法二：使用API接口
```bash
# 查看当前算法
GET http://localhost:8888/api/loadbalancer/algorithm

# 切换算法（需要重启生效）
POST http://localhost:8888/api/loadbalancer/algorithm/random
```

### 3. 测试负载均衡效果

#### 单次调用测试：
```bash
GET http://localhost:8888/api/test/single/1
```

#### 批量调用测试：
```bash
# 调用10次，观察负载分布
GET http://localhost:8888/api/test/loadbalancer/1/10
```

#### 原始订单接口测试：
```bash
POST http://localhost:8888/api/orders/1
Content-Type: application/json

{
  "id": 1,
  "userId": 1001,
  "amount": 100.0
}
```

### 4. 查看服务实例

```bash
# 查看所有服务
GET http://localhost:8888/api/loadbalancer/services

# 查看特定服务实例
GET http://localhost:8888/api/loadbalancer/instances/goods-service
```

## 算法详解

### 1. 轮询算法 (Round Robin)
- 按顺序依次调用每个服务实例
- 适用于服务实例性能相近的场景

### 2. 随机算法 (Random)
- 随机选择服务实例
- 在大量请求下趋向于均匀分布

### 3. 自定义权重算法 (Custom)
- 基于服务实例端口号计算权重
- 端口号最后一位数字作为权重因子
- 权重越高，被选中的概率越大

## 配置文件说明

### application.yml 关键配置

```yaml
# 服务配置
spring:
  application:
    name: order-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
    loadbalancer:
      enabled: true
      ribbon:
        enabled: false

# 负载均衡算法配置
loadbalancer:
  algorithm: round-robin

# 日志配置
logging:
  level:
    com.hnguigu.springcloud: DEBUG
    org.springframework.cloud.loadbalancer: DEBUG
```

## 核心类说明

### 1. LoadBalancerConfig
- Spring Cloud LoadBalancer配置类
- 根据配置动态创建负载均衡器实例

### 2. CustomReactiveLoadBalancer
- 自定义负载均衡器实现
- 基于权重的选择算法

### 3. LoadBalancerController
- 负载均衡管理接口
- 提供算法切换和服务查看功能

### 4. LoadBalancerTestController
- 负载均衡测试接口
- 用于验证不同算法的效果

## 注意事项

1. **算法切换**: 当前实现需要重启应用才能生效，生产环境建议使用配置中心实现动态刷新
2. **服务发现**: 确保Nacos服务器正常运行，服务能够正确注册
3. **日志观察**: 开启DEBUG日志可以观察负载均衡的详细过程
4. **性能考虑**: 自定义算法可能会增加选择开销，请根据实际需求选择

## 扩展功能

### 1. 添加新的负载均衡算法
1. 实现 `ReactorLoadBalancer<ServiceInstance>` 接口
2. 在 `LoadBalancerConfig` 中添加新的case分支
3. 更新配置文件和文档

### 2. 集成配置中心
可以集成Spring Cloud Config或Nacos Config实现动态配置刷新：
```java
@RefreshScope
@Component
public class DynamicLoadBalancerConfig {
    @Value("${loadbalancer.algorithm:round-robin}")
    private String algorithm;
    // ...
}
```

### 3. 监控和指标
可以添加Micrometer指标收集负载均衡的统计信息：
```java
@Component
public class LoadBalancerMetrics {
    private final Counter requestCounter;
    // ...
}
```

## 故障排除

### 1. 服务调用失败
- 检查Nacos服务器是否正常运行
- 确认服务实例是否正确注册
- 查看网络连接是否正常

### 2. 负载均衡不生效
- 确认LoadBalancer配置是否正确
- 检查是否有多个服务实例运行
- 查看日志确认算法是否正确加载

### 3. 自定义算法不工作
- 确认自定义类是否正确实现接口
- 检查配置文件中的算法名称是否匹配
- 查看Spring容器是否正确创建Bean
