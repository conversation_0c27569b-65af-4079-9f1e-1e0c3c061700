package com.hnguigu.springcloud.service.impl;


import com.hnguigu.springcloud.entity.Goods;
import com.hnguigu.springcloud.service.GoodsService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
public class GoodsServiceImpl implements GoodsService {

    @Value("${server.port}")
    private Integer port;

    @Override
    public Goods findGoodsById(Long id) {
        if (ObjectUtils.isEmpty(id) || id <= 0) {
            throw new IllegalArgumentException("id必须大于0");
        }

        Goods goods = new Goods();
        goods.setId(id);
        goods.setName("商品" + id + "-" + port);
        goods.setPrice(200D);

        return goods;
    }
}
