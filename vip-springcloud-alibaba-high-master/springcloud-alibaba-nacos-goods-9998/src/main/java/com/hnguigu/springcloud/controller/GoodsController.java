package com.hnguigu.springcloud.controller;

import com.hnguigu.springcloud.entity.Goods;
import com.hnguigu.springcloud.service.GoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/goods")
public class GoodsController {

    @Autowired
    private GoodsService goodsService;

    @GetMapping("/{id}")
    public Goods findGoodsById(@PathVariable Long id) {
        return this.goodsService.findGoodsById(id);
    }
}
