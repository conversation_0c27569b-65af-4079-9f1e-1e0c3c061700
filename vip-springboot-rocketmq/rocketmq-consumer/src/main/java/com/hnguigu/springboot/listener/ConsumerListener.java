package com.hnguigu.springboot.listener;

import com.hnguigu.springboot.entity.User;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

@Component
@RocketMQMessageListener(topic = "springboot-rocketmq-topic", consumerGroup = "consumer-group")
public class ConsumerListener implements RocketMQListener<User> {

    /*@Override
    public void onMessage(String message) {
        System.out.println("ConsumerListener onMessage: " + message);
    }*/

    @Override
    public void onMessage(User user) {
        System.out.println("ConsumerListener onMessage: " + user);
    }
}
