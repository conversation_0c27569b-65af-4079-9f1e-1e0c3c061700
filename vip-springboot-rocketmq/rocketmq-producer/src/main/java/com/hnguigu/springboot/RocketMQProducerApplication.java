package com.hnguigu.springboot;

import com.hnguigu.springboot.service.ProducerService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication
public class RocketMQProducerApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(RocketMQProducerApplication.class, args);
        ProducerService producerService = context.getBean(ProducerService.class);
        producerService.sendMessageInTransaction("springboot-rocketmq-topic", "Hello World");
    }
}
