package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.service.ProducerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/producers")
public class ProducerController {

    @Autowired
    private ProducerService producerService;

    @PostMapping
    public String send(String message) {
        this.producerService.sendMessage(message);
        return "success";
    }

    @PostMapping("/send")
    public String send(@RequestBody User user) {
        this.producerService.sendMessage(user);
        return "success";
    }

    @PostMapping("/sendMessageInTransaction")
    public String sendMessageInTransaction(String topic, String msg) {
        this.producerService.sendMessageInTransaction(topic, msg);
        return "success";
    }


}
