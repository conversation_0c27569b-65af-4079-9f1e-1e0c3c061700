export function setupCounter(element) {
  let counter = 0
  const setCounter = (count) => {
    counter = count
    element.innerHTML = `count is ${counter}`
  }
  element.addEventListener('click', () => setCounter(counter + 1))
  setCounter(0)
}

// 弱类型语言
let count = 100
count = 'helloworld'
console.log(typeof count) // string

const fun = (a,b) => {
    return a + b
}

// let result = fun(100,200);
// console.log(result)

let result = fun('100','200');
console.log(result)
