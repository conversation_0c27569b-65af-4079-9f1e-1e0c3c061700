# JWT工具类使用说明

本项目提供了完整的JWT（JSON Web Token）工具类，用于处理用户认证和授权。

## 主要组件

### 1. JwtProperties - JWT配置属性类
位置：`src/main/java/com/hnguigu/springboot/config/JwtProperties.java`

配置JWT相关参数：
- `secret`: JWT密钥
- `expiration`: JWT过期时间（秒）
- `refreshExpiration`: 刷新token过期时间（秒）
- `issuer`: JWT发行者
- `subject`: JWT主题
- `tokenPrefix`: JWT token前缀
- `headerName`: JWT header名称

### 2. JwtUtils - JWT工具类
位置：`src/main/java/com/hnguigu/springboot/utils/JwtUtils.java`

提供以下功能：
- 生成JWT token
- 解析JWT token
- 验证JWT token
- 刷新JWT token
- 从请求头获取token
- 检查token是否过期

### 3. JwtController - JWT控制器
位置：`src/main/java/com/hnguigu/springboot/controller/JwtController.java`

提供REST API接口：
- `/jwt/generate` - 生成token
- `/jwt/validate` - 验证token
- `/jwt/refresh` - 刷新token
- `/jwt/userinfo` - 获取用户信息
- `/jwt/parse` - 解析token

## 配置说明

在 `application.yml` 中添加JWT配置：

```yaml
jwt:
  secret: mySecretKey123456789abcdefghijklmnopqrstuvwxyz
  expiration: 604800  # 7天
  refresh-expiration: 2592000  # 30天
  issuer: hnguigu-security
  subject: user-authentication
  token-prefix: "Bearer "
  header-name: Authorization
```

## 使用示例

### 1. 生成JWT Token

```java
@Autowired
private JwtUtils jwtUtils;

// 根据用户名生成token
String token = jwtUtils.generateToken("username");

// 根据用户名和权限生成token
List<String> authorities = Arrays.asList("p1", "p2", "ROLE_USER");
String token = jwtUtils.generateToken("username", authorities);

// 根据UserDetails生成token
String token = jwtUtils.generateToken(userDetails);
```

### 2. 验证JWT Token

```java
boolean isValid = jwtUtils.validateToken(token);
if (isValid) {
    // token有效
    String username = jwtUtils.getUsernameFromToken(token);
    List<String> authorities = jwtUtils.getAuthoritiesFromToken(token);
}
```

### 3. 刷新JWT Token

```java
if (jwtUtils.canTokenBeRefreshed(oldToken)) {
    String newToken = jwtUtils.refreshToken(oldToken);
}
```

### 4. 从请求头获取Token

```java
String authHeader = request.getHeader("Authorization");
String token = jwtUtils.getTokenFromHeader(authHeader);
```

## API接口使用

### 生成Token
```bash
POST /jwt/generate?username=testuser
```

响应：
```json
{
  "access_token": "eyJhbGciOiJIUzUxMiJ9...",
  "refresh_token": "eyJhbGciOiJIUzUxMiJ9...",
  "token_type": "Bearer",
  "expires_in": 604800,
  "username": "testuser",
  "authorities": ["p1", "p2", "ROLE_USER"]
}
```

### 验证Token
```bash
GET /jwt/validate
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```

响应：
```json
{
  "valid": true,
  "username": "testuser",
  "authorities": ["p1", "p2", "ROLE_USER"],
  "expiration": "2025-07-11T08:00:00.000+00:00",
  "message": "Token有效"
}
```

### 刷新Token
```bash
POST /jwt/refresh
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```

响应：
```json
{
  "success": true,
  "access_token": "eyJhbGciOiJIUzUxMiJ9...",
  "token_type": "Bearer",
  "expires_in": 604800,
  "message": "Token刷新成功"
}
```

## 测试

运行测试类：`src/test/java/com/hnguigu/springboot/jwt/JwtTest.java`

测试包括：
- Token生成测试
- Token解析测试
- Token验证测试
- Token刷新测试
- 完整工作流程测试

```bash
mvn test -Dtest=JwtTest
```

## 安全注意事项

1. **密钥安全**：生产环境中使用强密钥，不要在代码中硬编码
2. **HTTPS**：生产环境中必须使用HTTPS传输token
3. **过期时间**：合理设置token过期时间，平衡安全性和用户体验
4. **存储安全**：客户端安全存储token，避免XSS攻击
5. **刷新机制**：实现合理的token刷新机制

## 依赖

项目使用以下JWT依赖：
```xml
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt</artifactId>
    <version>0.9.1</version>
</dependency>
```

## 集成Spring Security

JWT工具类可以与Spring Security集成，实现基于token的认证：

1. 创建JWT认证过滤器
2. 配置Security配置类
3. 实现token验证逻辑
4. 处理认证成功/失败

详细集成步骤请参考Spring Security相关文档。
