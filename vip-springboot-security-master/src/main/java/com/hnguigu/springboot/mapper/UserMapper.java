package com.hnguigu.springboot.mapper;

import com.hnguigu.springboot.dto.UserDTO;
import com.hnguigu.springboot.entity.Role;
import com.hnguigu.springboot.entity.User;

import java.util.List;

public interface UserMapper {

    User findByName(String name);

    /**
     * 根据用户编号查询用户信息,同时也查询出权限信息
     * 用户信息和权限信息整合到UserDTO
     * @param id
     * @return
     */
    List<UserDTO> findById(Long id);

    List<Role> findRolesById(Long id);
}
