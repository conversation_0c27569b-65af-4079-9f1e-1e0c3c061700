package com.hnguigu.springboot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * JWT配置属性类
 * 用于管理JWT相关的配置参数
 */
@Data
@Component
@ConfigurationProperties(prefix = "jwt")
public class JwtProperties {
    
    /**
     * JWT密钥
     */
    private String secret = "mySecretKey123456789";
    
    /**
     * JWT过期时间（秒）
     * 默认7天
     */
    private Long expiration = 7 * 24 * 60 * 60L;
    
    /**
     * JWT刷新token过期时间（秒）
     * 默认30天
     */
    private Long refreshExpiration = 30 * 24 * 60 * 60L;
    
    /**
     * JWT发行者
     */
    private String issuer = "hnguigu-security";
    
    /**
     * JWT主题
     */
    private String subject = "user-authentication";
    
    /**
     * JWT token前缀
     */
    private String tokenPrefix = "Bearer ";
    
    /**
     * JWT header名称
     */
    private String headerName = "Authorization";
}
