package com.hnguigu.springboot.config;


import com.hnguigu.springboot.handler.MyAuthenticationSuccessHandler;
import com.hnguigu.springboot.service.UserService;
import com.hnguigu.springboot.service.impl.UserServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.provisioning.JdbcUserDetailsManager;

import javax.sql.DataSource;

@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true,securedEnabled = true,jsr250Enabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    private MyAuthenticationSuccessHandler myAuthenticationSuccessHandler;

    @Autowired
    private UserService userService;

    /**
     * UserDetailsService 业务逻辑层对象
     *
     * @return
     */
    /*@Bean
    public UserDetailsService userDetailsService() {
        // 内存版UserDetailService
        InMemoryUserDetailsManager manager = new InMemoryUserDetailsManager();

        // 模拟数据
        manager.createUser(User.withUsername("zhangsan").password("123").authorities("p1").build());
        manager.createUser(User.withUsername("lisi").password("456").authorities("p2").build());
        manager.createUser(User.withUsername("wangwu").password("789").authorities("p1", "p2").build());
        return manager;
    }*/

    /*@Bean
    public UserDetailsService userDetailsService(DataSource dataSource) {
        JdbcUserDetailsManager jdbcUserDetailsManager = new JdbcUserDetailsManager();
        jdbcUserDetailsManager.setDataSource(dataSource);
        jdbcUserDetailsManager.setUserD
        return jdbcUserDetailsManager;
    }*/

    /**
     * 将后端的业务（根据用户名称查询用户）关联起来
     * @param auth
     * @throws Exception
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(this.userService).passwordEncoder(bCryptPasswordEncoder());
    }

    /**
     * 密码不加密
     *
     * @return
     */
    /*@Bean
    public PasswordEncoder passwordEncoder() {
        // 不加密的编码器。真正开发中是需要加密。BCryptPasswordEncoder
        return NoOpPasswordEncoder.getInstance();
    }*/

    /**
     * BCryptPasswordEncoder 加密器
     * @return
     */
    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable().authorizeRequests()
//                .antMatchers("/r/r1").hasAuthority("p1")
//                .antMatchers("/r/r2").hasAuthority("p2")
//                .antMatchers("/r/r3").access("hasAuthority('p1') and hasAuthority('p2')")
                .antMatchers("/r/**").authenticated().anyRequest().permitAll().and().formLogin().loginPage("/loginUI")     // 指定登录页面的跳转的API
                .loginProcessingUrl("/login")    // 登录的处理的请求API
//                .successForwardUrl("/loginSuccess");
                .successHandler(myAuthenticationSuccessHandler);

    }
}
