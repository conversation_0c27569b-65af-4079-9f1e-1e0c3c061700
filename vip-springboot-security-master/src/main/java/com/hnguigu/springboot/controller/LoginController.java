package com.hnguigu.springboot.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Slf4j
public class LoginController {

    @GetMapping("/loginUI")
    public String login() {
        return "login";
    }

    @GetMapping("/r/r1")
    @ResponseBody
    @PreAuthorize("hasAuthority('p1')")
    public String r1() {
        log.info("访问r1资源");
        return "访问r1资源";
    }

    @GetMapping("/r/r2")
    @ResponseBody
    @PreAuthorize("hasAuthority('p2') and hasAuthority('p3')")
    public String r2() {
        log.info("访问r2资源");
        return "访问r2资源";
    }

    @GetMapping("/r/r3")
    @ResponseBody
    public String r3() {
        log.info("访问r3资源");
        return "访问r3资源";
    }

    @GetMapping("/loginSuccess")
    public String success() {
        log.info("登录成功");
        return "success";
    }
}
