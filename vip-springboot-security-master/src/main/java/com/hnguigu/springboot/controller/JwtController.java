package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JWT相关的控制器
 * 提供JWT token的生成、验证、刷新等API
 */
@RestController
@RequestMapping("/jwt")
@Slf4j
public class JwtController {

    @Autowired
    private JwtUtils jwtUtils;

    /**
     * 生成JWT token（示例接口）
     * 
     * @param username 用户名
     * @return JWT token信息
     */
    @PostMapping("/generate")
    public Map<String, Object> generateToken(@RequestParam String username) {
        log.info("为用户 {} 生成JWT token", username);
        
        // 模拟用户权限
        List<String> authorities = Arrays.asList("p1", "p2", "ROLE_USER");
        
        // 生成访问token
        String accessToken = jwtUtils.generateToken(username, authorities);
        
        // 生成刷新token
        String refreshToken = jwtUtils.generateRefreshToken(username);
        
        Map<String, Object> result = new HashMap<>();
        result.put("access_token", accessToken);
        result.put("refresh_token", refreshToken);
        result.put("token_type", "Bearer");
        result.put("expires_in", 604800); // 7天
        result.put("username", username);
        result.put("authorities", authorities);
        
        return result;
    }

    /**
     * 验证JWT token
     * 
     * @param request HTTP请求
     * @return 验证结果
     */
    @GetMapping("/validate")
    public Map<String, Object> validateToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        String token = jwtUtils.getTokenFromHeader(authHeader);
        
        Map<String, Object> result = new HashMap<>();
        
        if (token == null) {
            result.put("valid", false);
            result.put("message", "Token不存在");
            return result;
        }
        
        boolean isValid = jwtUtils.validateToken(token);
        result.put("valid", isValid);
        
        if (isValid) {
            String username = jwtUtils.getUsernameFromToken(token);
            List<String> authorities = jwtUtils.getAuthoritiesFromToken(token);
            
            result.put("username", username);
            result.put("authorities", authorities);
            result.put("expiration", jwtUtils.getExpirationDateFromToken(token));
            result.put("message", "Token有效");
        } else {
            result.put("message", "Token无效或已过期");
        }
        
        return result;
    }

    /**
     * 刷新JWT token
     * 
     * @param request HTTP请求
     * @return 新的token信息
     */
    @PostMapping("/refresh")
    public Map<String, Object> refreshToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        String token = jwtUtils.getTokenFromHeader(authHeader);
        
        Map<String, Object> result = new HashMap<>();
        
        if (token == null) {
            result.put("success", false);
            result.put("message", "Token不存在");
            return result;
        }
        
        if (!jwtUtils.canTokenBeRefreshed(token)) {
            result.put("success", false);
            result.put("message", "Token无法刷新");
            return result;
        }
        
        String newToken = jwtUtils.refreshToken(token);
        if (newToken != null) {
            result.put("success", true);
            result.put("access_token", newToken);
            result.put("token_type", "Bearer");
            result.put("expires_in", 604800); // 7天
            result.put("message", "Token刷新成功");
        } else {
            result.put("success", false);
            result.put("message", "Token刷新失败");
        }
        
        return result;
    }

    /**
     * 获取当前用户信息（需要认证）
     * 
     * @return 用户信息
     */
    @GetMapping("/userinfo")
    public Map<String, Object> getUserInfo() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        Map<String, Object> result = new HashMap<>();
        
        if (authentication != null && authentication.isAuthenticated()) {
            Object principal = authentication.getPrincipal();
            
            if (principal instanceof UserDetails) {
                UserDetails userDetails = (UserDetails) principal;
                result.put("username", userDetails.getUsername());
                result.put("authorities", userDetails.getAuthorities());
                result.put("authenticated", true);
            } else {
                result.put("username", principal.toString());
                result.put("authenticated", true);
            }
        } else {
            result.put("authenticated", false);
            result.put("message", "用户未认证");
        }
        
        return result;
    }

    /**
     * 解析token信息（不验证有效性）
     * 
     * @param token JWT token
     * @return token信息
     */
    @GetMapping("/parse")
    public Map<String, Object> parseToken(@RequestParam String token) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = jwtUtils.getUsernameFromToken(token);
            List<String> authorities = jwtUtils.getAuthoritiesFromToken(token);
            
            result.put("username", username);
            result.put("authorities", authorities);
            result.put("expiration", jwtUtils.getExpirationDateFromToken(token));
            result.put("expired", jwtUtils.isTokenExpired(token));
            result.put("success", true);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "Token解析失败: " + e.getMessage());
        }
        
        return result;
    }
}
