package com.hnguigu.springboot.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class JwtUtils1 {
    // 密钥
    private static final String SECRET_KEY = "yourSecretKey";

    // token有效期（毫秒）
    private static final long EXPIRATION = 1000 * 60 * 60 * 24; // 24小时

    /**
     * 根据用户名称生成Token
     * 生成token
     */
    public static String generateToken(String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("username", username);

        return Jwts.builder().setClaims(claims).setSubject(username).setIssuedAt(new Date()).setExpiration(new Date(System.currentTimeMillis() + EXPIRATION)).signWith(SignatureAlgorithm.HS256, SECRET_KEY).compact();
    }

    /**
     * 解析token
     */
    public static Claims parseToken(String token) {
        return Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(token).getBody();
    }

    /**
     * 验证token是否有效
     */
    public static boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从token中获取用户名
     */
    public static String getUsernameFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.getSubject();
    }

    /**
     * 刷新token
     * 验证原token是否有效，然后创建一个新的token
     *
     * @param token 原token
     * @return 新token，如果原token无效则返回null
     */
    public static String refreshToken(String token) {
        try {
            // 验证token
            if (!validateToken(token)) {
                return null;
            }

            // 解析token获取claims
            Claims claims = parseToken(token);
            String username = claims.getSubject();

            // 创建新token
            return generateToken(username);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 判断token是否即将过期
     *
     * @param token   token
     * @param minutes 过期前的分钟数
     * @return 是否即将过期
     */
    public static boolean isTokenAboutToExpire(String token, int minutes) {
        try {
            Claims claims = parseToken(token);
            Date expiration = claims.getExpiration();

            // 计算当前时间加上指定分钟数后的时间
            Date now = new Date();
            Date aboutToExpire = new Date(now.getTime() + minutes * 60 * 1000);

            // 如果过期时间在aboutToExpire之前，则表示即将过期
            return expiration.before(aboutToExpire);
        } catch (Exception e) {
            return false;
        }
    }
}
