package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.Permission;
import com.hnguigu.springboot.entity.Role;
import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.mapper.RoleMapper;
import com.hnguigu.springboot.mapper.UserMapper;
import com.hnguigu.springboot.service.UserService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class UserServiceImpl implements UserService, UserDetailsService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SimpleGrantedAuthority simpleGrantedAuthority = null;

        if (StringUtils.isEmpty(username)) {
            throw new IllegalArgumentException("用户名不能为空");
        }

        User user = this.userMapper.findByName(username);

        // TODO 根据用户编号查询该用户的权限。外连接查询
        List<Permission> permissionList = this.findPermissionListById(user.getId());

        List<GrantedAuthority> authorities = new ArrayList<>();
        for (Permission permission : permissionList) {
            String name = permission.getName();
            simpleGrantedAuthority = new SimpleGrantedAuthority(name);
            authorities.add(simpleGrantedAuthority);
        }

        org.springframework.security.core.userdetails.User finalUser =
                new org.springframework.security.core.userdetails.User(user.getName(), user.getPassword(), authorities);
        return finalUser;
    }

    @Override
    public List<Permission> findPermissionListById(Long id) {
        Set<Permission> permissionSet = new HashSet<>();

        if (ObjectUtils.isEmpty(id) || id <= 0) {
            throw new IllegalArgumentException("用户编号不能为空");
        }

        // TODO 根据用户编号查询角色列表
        List<Role> roleList = this.userMapper.findRolesById(id);

        if (!CollectionUtils.isEmpty(roleList)) {
            for (Role role : roleList) {
                // TODO 根据角色编号查询权限列表
                List<Permission> permissionList = this.roleMapper.findPermissionsById(role.getId());
//                Collections.addAll(finalPermissionList,permissionList.toArray(new Permission[0]));

                CollectionUtils.addAll(permissionSet, permissionList.iterator());
            }
        }

        return new ArrayList<>(permissionSet);
    }
}
