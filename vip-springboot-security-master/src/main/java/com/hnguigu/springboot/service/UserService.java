package com.hnguigu.springboot.service;

import com.hnguigu.springboot.dto.UserDTO;
import com.hnguigu.springboot.entity.Permission;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.List;

public interface UserService extends UserDetailsService {

    /**
     * 根据用户编号查询用户的权限
     * @param id
     * @return
     */
    List<Permission> findPermissionListById(Long id);


}
