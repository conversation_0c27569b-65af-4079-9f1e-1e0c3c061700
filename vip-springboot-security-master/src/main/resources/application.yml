server:
  port: 8888

spring:
  application:
    name: security-service
  datasource:
    username: root
    password: root
    driver-class-name: com.mysql.jdbc.Driver
    url: ****************************************************************************************************

  mvc:
    view:
      prefix: /WEB-INF/views/
      suffix: .jsp

mybatis:
  configuration:
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    cache-enabled: true
  mapper-locations: classpath:mybatis/*Mapper.xml
  type-aliases-package: com.hnguigu.springboot.entity,com.hnguigu.springboot.dto

# JWT配置
jwt:
  # JWT密钥（生产环境中应该使用更复杂的密钥）
  secret: mySecretKey123456789abcdefghijklmnopqrstuvwxyz
  # JWT过期时间（秒）- 7天
  expiration: 604800
  # 刷新token过期时间（秒）- 30天
  refresh-expiration: 2592000
  # JWT发行者
  issuer: hnguigu-security
  # JWT主题
  subject: user-authentication
  # JWT token前缀
  token-prefix: "Bearer "
  # JWT header名称
  header-name: Authorization
