<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hnguigu.springboot.mapper.UserMapper">

    <resultMap id="UserDTOResultMap" type="UserDTO">
        <association property="user" javaType="User" resultMap="UserBaseResultMap"/>
        <collection property="permissionList" ofType="Permission"
                    resultMap="com.hnguigu.springboot.mapper.PermissionMapper.PermissionResultMap">
        </collection>
    </resultMap>

    <resultMap id="UserBaseResultMap" type="User">
        <id property="id" column="user_id"/>
        <result property="name" column="user_name"/>
        <result property="password" column="user_password"/>
        <result property="salary" column="user_salary"/>
        <result property="birthday" column="user_birthday"/>
    </resultMap>

    <select id="findByName" resultMap="UserBaseResultMap">
        SELECT USER_ID, USER_NAME, USER_PASSWORD, USER_SALARY, USER_BIRTHDAY
        FROM SECURITY_USER
        WHERE USER_NAME = #{name}
    </select>

    <select id="findById" resultMap="UserDTOResultMap">
        SELECT DISTINCT SP.PERMISSION_ID,
                        SP.PERMISSION_NAME,
                        SP.PERMISSION_URL,
                        SP.PARENT_ID,
                        SU.USER_ID,
                        SU.USER_NAME,
                        SU.USER_PASSWORD,
                        SU.USER_SALARY,
                        SU.USER_BIRTHDAY
        FROM SECURITY_PERMISSION SP
                 LEFT JOIN SECURITY_ROLE_PERMISSION SRP ON SP.PERMISSION_ID = SRP.PERMISSION_ID
                 LEFT JOIN SECURITY_USER_ROLE SUR ON SRP.ROLE_ID = SUR.ROLE_ID
                 LEFT JOIN SECURITY_USER SU ON SUR.USER_ID = SU.USER_ID
        WHERE SU.USER_ID = #{id}
    </select>

    <select id="findRolesById" resultType="com.hnguigu.springboot.entity.Role">
        SELECT SR.ROLE_ID          ID,
               SR.ROLE_NAME        NAME,
               SR.ROLE_DESCRIPTION DESCRIPTION
        FROM SECURITY_ROLE SR
                 LEFT JOIN SECURITY_USER_ROLE SUR ON SR.ROLE_ID = SUR.ROLE_ID
        WHERE SUR.USER_ID = #{id}
    </select>

</mapper>
