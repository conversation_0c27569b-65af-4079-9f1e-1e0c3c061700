<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hnguigu.springboot.mapper.RoleMapper">

    <select id="findPermissionsById" resultType="com.hnguigu.springboot.entity.Permission">
        SELECT SP.PERMISSION_ID   ID,
               SP.PERMISSION_NAME NAME,
               SP.PERMISSION_URL  URL,
               SP.PARENT_ID       PARENTID
        FROM SECURITY_PERMISSION SP
                 LEFT JOIN SPRINGBOOT_SECURITY.SECURITY_ROLE_PERMISSION SRP ON SP.PERMISSION_ID = SRP.PERMISSION_ID
        WHERE SRP.ROLE_ID = #{id}
    </select>
</mapper>
