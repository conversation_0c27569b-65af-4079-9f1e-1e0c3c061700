package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.Permission;
import com.hnguigu.springboot.service.UserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class UserServiceImplTest {

    @Autowired
    private UserService userService;

    @Test
    public void testFindPermissionListById() {
        List<Permission> permissionList = this.userService.findPermissionListById(2L);
        permissionList.stream().forEach(System.out::println);
    }
}
