package com.hnguigu.springboot.jwt;

import com.hnguigu.springboot.config.JwtProperties;
import com.hnguigu.springboot.utils.JwtUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * JWT工具类测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class JwtTest {

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private JwtProperties jwtProperties;

    private UserDetails userDetails;
    private String username = "testuser";
    private List<String> authorities = Arrays.asList("p1", "p2", "ROLE_USER");

    @Before
    public void setUp() {
        // 创建测试用户
        List<GrantedAuthority> grantedAuthorities = Arrays.asList(
                new SimpleGrantedAuthority("p1"),
                new SimpleGrantedAuthority("p2"),
                new SimpleGrantedAuthority("ROLE_USER")
        );

        userDetails = new User(username, "password", grantedAuthorities);
    }

    @Test
    public void testGenerateToken() {
        System.out.println("=== 测试生成JWT Token ===");

        // 测试根据UserDetails生成token
        String token1 = jwtUtils.generateToken(userDetails);
        System.out.println("根据UserDetails生成的token: " + token1);

        // 测试根据用户名生成token
        String token2 = jwtUtils.generateToken(username);
        System.out.println("根据用户名生成的token: " + token2);

        // 测试根据用户名和权限生成token
        String token3 = jwtUtils.generateToken(username, authorities);
        System.out.println("根据用户名和权限生成的token: " + token3);

        // 验证token不为空
        assert token1 != null && !token1.isEmpty();
        assert token2 != null && !token2.isEmpty();
        assert token3 != null && !token3.isEmpty();

        System.out.println("Token生成测试通过！\n");
    }

    @Test
    public void testParseToken() {
        System.out.println("=== 测试解析JWT Token ===");

        // 生成token
        String token = jwtUtils.generateToken(username, authorities);
        System.out.println("生成的token: " + token);

        // 解析用户名
        String parsedUsername = jwtUtils.getUsernameFromToken(token);
        System.out.println("解析出的用户名: " + parsedUsername);

        // 解析权限
        List<String> parsedAuthorities = jwtUtils.getAuthoritiesFromToken(token);
        System.out.println("解析出的权限: " + parsedAuthorities);

        // 解析过期时间
        Date expirationDate = jwtUtils.getExpirationDateFromToken(token);
        System.out.println("过期时间: " + expirationDate);

        // 验证解析结果
        assert username.equals(parsedUsername);
        assert authorities.equals(parsedAuthorities);
        assert expirationDate != null;

        System.out.println("Token解析测试通过！\n");
    }

    @Test
    public void testValidateToken() {
        System.out.println("=== 测试验证JWT Token ===");

        // 生成有效token
        String validToken = jwtUtils.generateToken(username, authorities);
        System.out.println("有效token: " + validToken);

        // 验证有效token
        boolean isValid = jwtUtils.validateToken(validToken);
        System.out.println("Token验证结果: " + isValid);

        // 测试无效token
        String invalidToken = "invalid.token.here";
        boolean isInvalid = jwtUtils.validateToken(invalidToken);
        System.out.println("无效Token验证结果: " + isInvalid);

        // 测试空token
        boolean isNullValid = jwtUtils.validateToken(null);
        System.out.println("空Token验证结果: " + isNullValid);

        // 验证结果
        assert isValid == true;
        assert isInvalid == false;
        assert isNullValid == false;

        System.out.println("Token验证测试通过！\n");
    }

    @Test
    public void testRefreshToken() {
        System.out.println("=== 测试刷新JWT Token ===");

        // 生成原始token
        String originalToken = jwtUtils.generateToken(username, authorities);
        System.out.println("原始token: " + originalToken);

        // 检查是否可以刷新
        boolean canRefresh = jwtUtils.canTokenBeRefreshed(originalToken);
        System.out.println("是否可以刷新: " + canRefresh);

        // 刷新token
        String refreshedToken = jwtUtils.refreshToken(originalToken);
        System.out.println("刷新后的token: " + refreshedToken);

        // 验证刷新后的token
        boolean isRefreshedValid = jwtUtils.validateToken(refreshedToken);
        System.out.println("刷新后token是否有效: " + isRefreshedValid);

        // 验证用户名是否一致
        String refreshedUsername = jwtUtils.getUsernameFromToken(refreshedToken);
        System.out.println("刷新后token的用户名: " + refreshedUsername);

        // 验证结果
        assert canRefresh == true;
        assert refreshedToken != null;
        assert isRefreshedValid == true;
        assert username.equals(refreshedUsername);

        System.out.println("Token刷新测试通过！\n");
    }

    @Test
    public void testGenerateRefreshToken() {
        System.out.println("=== 测试生成刷新Token ===");

        // 生成刷新token
        String refreshToken = jwtUtils.generateRefreshToken(username);
        System.out.println("刷新token: " + refreshToken);

        // 验证刷新token
        boolean isValid = jwtUtils.validateToken(refreshToken);
        System.out.println("刷新token是否有效: " + isValid);

        // 从刷新token中获取用户名
        String parsedUsername = jwtUtils.getUsernameFromToken(refreshToken);
        System.out.println("从刷新token解析的用户名: " + parsedUsername);

        // 验证结果
        assert refreshToken != null;
        assert isValid == true;
        assert username.equals(parsedUsername);

        System.out.println("刷新Token生成测试通过！\n");
    }

    @Test
    public void testGetTokenFromHeader() {
        System.out.println("=== 测试从请求头获取Token ===");

        // 生成token
        String token = jwtUtils.generateToken(username);

        // 模拟请求头
        String authHeader = jwtProperties.getTokenPrefix() + token;
        System.out.println("模拟的认证头: " + authHeader);

        // 从请求头获取token
        String extractedToken = jwtUtils.getTokenFromHeader(authHeader);
        System.out.println("提取的token: " + extractedToken);

        // 验证提取的token
        boolean isValid = jwtUtils.validateToken(extractedToken);
        System.out.println("提取的token是否有效: " + isValid);

        // 测试无效的请求头
        String invalidHeader = "InvalidHeader " + token;
        String nullToken = jwtUtils.getTokenFromHeader(invalidHeader);
        System.out.println("无效请求头提取结果: " + nullToken);

        // 验证结果
        assert token.equals(extractedToken);
        assert isValid == true;
        assert nullToken == null;

        System.out.println("从请求头获取Token测试通过！\n");
    }

    @Test
    public void testTokenExpiration() {
        System.out.println("=== 测试Token过期检查 ===");

        // 生成token
        String token = jwtUtils.generateToken(username);
        System.out.println("生成的token: " + token);

        // 检查是否过期
        boolean isExpired = jwtUtils.isTokenExpired(token);
        System.out.println("Token是否过期: " + isExpired);

        // 获取过期时间
        Date expirationDate = jwtUtils.getExpirationDateFromToken(token);
        System.out.println("Token过期时间: " + expirationDate);

        // 验证结果
        assert isExpired == false;
        assert expirationDate != null;
        assert expirationDate.after(new Date());

        System.out.println("Token过期检查测试通过！\n");
    }

    @Test
    public void testCompleteWorkflow() {
        System.out.println("=== 测试完整工作流程 ===");

        // 1. 生成token
        String token = jwtUtils.generateToken(userDetails);
        System.out.println("1. 生成token: " + token);

        // 2. 验证token
        boolean isValid = jwtUtils.validateToken(token);
        System.out.println("2. 验证token: " + isValid);

        // 3. 解析用户信息
        String parsedUsername = jwtUtils.getUsernameFromToken(token);
        List<String> parsedAuthorities = jwtUtils.getAuthoritiesFromToken(token);
        System.out.println("3. 解析用户名: " + parsedUsername);
        System.out.println("   解析权限: " + parsedAuthorities);

        // 4. 刷新token
        String newToken = jwtUtils.refreshToken(token);
        System.out.println("4. 刷新token: " + newToken);

        // 5. 验证新token
        boolean isNewTokenValid = jwtUtils.validateToken(newToken);
        System.out.println("5. 验证新token: " + isNewTokenValid);

        // 验证整个流程
        assert isValid == true;
        assert username.equals(parsedUsername);
        assert newToken != null;
        assert isNewTokenValid == true;

        System.out.println("完整工作流程测试通过！");
    }
}
