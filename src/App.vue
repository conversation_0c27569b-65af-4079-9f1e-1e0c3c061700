<script setup>
import Edit from './components/Edit.vue'
import {onMounted, ref} from "vue";
import axios from "axios";

// TODO: 列表渲染
// 声明一个响应式变量。存放数组
const userList = ref([])

const getUserList = async () => {

  // res是发送异步请求返回的结果。这个结果的结构并不清楚
  const res = await axios.get('/list')

  // 将res中的结果赋值给userList
  userList.value = res.data
}

onMounted(() => {
  getUserList()
})


// TODO: 删除功能
const handleDeleteUser = async (id) => {
  await axios.delete(`/del/${id}`)

  // 删除用户之后重新查询数据（刷新表格）
  getUserList()
}


// TODO: 编辑功能
// 模板引用
const editRef = ref(null)
const user = ref({})
const handleEditUI = (row) => {
  editRef.value.showEditDialog(row)
}

</script>

<template>
  <div class="app">
    <el-table :data="userList">
      <el-table-column label="ID" prop="id"></el-table-column>
      <el-table-column label="姓名" prop="name" width="150"></el-table-column>
      <el-table-column label="籍贯" prop="place"></el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{row}">
          <el-button type="primary" link @click="handleEditUI(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDeleteUser(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <Edit ref="editRef" @get-list="getUserList"/>
</template>

<style scoped>
.app {
  width: 980px;
  margin: 100px auto 0;
}
</style>
