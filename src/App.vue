<script setup lang="ts">
import List from './components/List.vue'
import {onMounted, ref} from "vue";
import axios from "axios";
import dayjs from "dayjs";

// 定义Channel类型
type ChannelType = {
  id: number,
  name: string
}

// 频道的返回值类型
type ChannelRes = {
  message: string,
  data: {
    channels: ChannelType[]
  }
}

onMounted(() => {

  // 查询频道列表
  getChannelList()
})

const channelRes = ref<ChannelRes>()
const getChannelList = async () => {
  const res = await axios.get('http://geek.itheima.net/v1_0/channels')
  channelRes.value = res.data
}

const handleChange = (id: number) => {

  // 根据频道编号查询文章列表
  getArticleListByChannelId(id)

}

type ArticleType = {
  id: number,
  title: string,
  cover: {
    type: number,
    images: string[]
  },
  aut_id: number,
  comm_count: number,
  pubdate: string,
  aut_name: string,
  like_count: number,
  attitude: number,
  is_top: number
}

type ArticleRes = {
  message: string,
  data: {
    pre_timestamp: string,
    results: ArticleType[]
  }
}

const getArticleListByChannelId = async (id: number) => {
  const res = await axios.get(`http://geek.itheima.net/v1_0/channels/${id}/articles`)

  //
}


const date = dayjs('2018-08-08') // parse
console.log(date)

dayjs().format('{YYYY} MM-DDTHH:mm:ss SSS [Z] A') // display

dayjs().set('month', 3).month() // get & set

dayjs().add(1, 'year') // manipulate

dayjs().isBefore(dayjs()) // query


</script>

<template>
  <!-- tab切换 -->
  <van-tabs>
    <van-tab @change="handleChange(channel.id)" v-for="channel in channelRes.data.channels" :title="channel.name"
             :key="channel.id">
      <!-- 文章列表组件 -->
      <List/>
    </van-tab>

  </van-tabs>

</template>


