/*
/!*
/!*const add = (a: number, b: number): number => {
    return a + b
}

let result = add(1, 2);
console.log(result)

// 类型注解
//简单类型（number,string,boolean,undefined,null）的类型注解
const count: number = 100
console.log(typeof count) // number

const str: string = 'hello world'
console.log(typeof str)

const flag: boolean = false
console.log(typeof flag)

// undefined，null

// 引用数据类型
// 数组
const arr1: number[] = [1, 2, 3, 4, 5]
arr1.forEach(item => {
    console.log(item)
})

const arr2: Array<number> = [1, 2, 3, 4, 5]
let s = arr2.join('-');
console.log(s)
arr2.forEach(item => {
    console.log(item)
})

const arr3: string[] = ['ibm', 'sun', 'apple', 'google']
arr3.forEach(item => {
    console.log(item)
})

// 联合类型
let num: number | string = 'helloworld'
num = 100
// num = false
console.log(num)

// arr4是一个数组。数组中可以存放number类型或string类型的成员
const arr4: (number | string)[] = [1, 2, 3, 4, 'aaaa']
arr4.forEach(item => {
    console.log(item)
})

// 别名类型
type MyType = (number | string)[]
const arr5: MyType = [1, 2, 3, 4, 'aaaa']
arr5.forEach(item => {
    console.log(item)
})*!/

// 有一个变量foo,要求添加类型注解，使其既可以赋值为number类型，也可以赋值为成员都是字符串的数组?

// const foo: (number | string)[] = []

// 函数类型注解。函数的参数以及返回值类型上加上类型注解
/!*const fun = (a: number, b: number): number => {
    return a + b
}

let result = fun(1, 2);
// let result1 = fun('100', 2);
console.log(result)

type funType = (a: number, b: number) => number
const addFun: funType = (a, b) => {
    return a + b
}

let result1 = addFun(100, 200);
console.log(result1)

function addFun1(a: number, b: number): number {
    return a + b
}

let result2 = addFun1(100, 200);
console.log(result2)*!/

// 可选参数 只能放在参数列表的最后
const addFun = (a: number, b?: number): number => {
    if (b) {
        return a + b
    } else {
        return a + 1
    }
}

let result = addFun(100, 200);
console.log(result)

const printMessage = (s: string): void => {
    console.log(s)
}

printMessage('helloworld')

/!*const arr1: number[] = [1, 2, 3, 4, 5]
arr1.forEach(item => console.log(item))

const arr2: Array<number> = [1, 2, 3, 4, 5]
arr2.forEach(item => console.log(item))

const arr3: (number | string)[] = [1, 2, 3, 'a', 'b']
arr3.forEach(item => console.log(item))

type ItemType = (number | string)[]
const arr4: ItemType = [1, 2, 3, 'a', 'b']
arr4.forEach(item => console.log(item))

const addFun = (a: number, b: number): number => {
    return a + b
}

let result = addFun(1, 2);
console.log(result)

type FunType = (a: number, b: number) => number
const addFun1: FunType = (a, b) => {
    return a + b
}

const result1 = addFun1(1, 2)
console.log(result1)*!/


/!*
* 编写一个arr2Str函数，作用为把数组转换为字符串，其中数组中既可以包含字符串和数字，分隔符也可
* 以进行自定义，类型为字符串类型，使用样例：
* 1. arr2Str( [1, 2, 3] , '-' ) -> '1-2-3'   join
* 2. arr2Str( [‘4’, ’5’] , ’&’ ) -> '4&5'
* *!/

/!*const arr2Str = (sourceArray: (number | string)[], split: string): string => {
    const defaultSplit = ','

    if (sourceArray == null || sourceArray.length === 0) {
        return ''
    }

    if (split == null || split === '') {
        split = defaultSplit
    }

    return sourceArray.join(split);
}

const result3 = arr2Str([1, 2, 3, 'Helloworld'], '-')
console.log(result3)*!/

// interface vs type
// 接口---规范   暴露接口不要暴露实现 开闭原则
// 不同的平台技术相互调用
/!*interface IUser {
    id: number,
    name: string,
    age: number
}

const user: IUser = {
    id: 1,
    name: 'ibm',
    age: 100
}

console.log(user.id, user.name, user.age)

// 接口的继承 extends
interface IGoods {
    id: number,
    name: string,
    price: number
}

// 折扣商品
interface DisGoods extends IGoods {
    disPrice: number
}

// xxx商品
interface XxxGoods extends IGoods {
    xxx: string
}

const disGoods: DisGoods = {
    id: 1,
    name: 'ibm',
    price: 100,
    disPrice: 90
}

const xxxGoods: XxxGoods = {
    id: 1,
    name: 'ibm',
    price: 100,
    xxx: 'xxx'
}

console.log(disGoods.id, disGoods.name, disGoods.price, disGoods.disPrice)
console.log(xxxGoods.id, disGoods.name, disGoods.price, xxxGoods.xxx)

// 后端向前端发送过来的数据，我们需要进行定义成接口或类型

interface User {
    id: string,
    name: string,
    place: string
}

interface ResInterface {
    status: number,
    statusText: string,
    data: User[]
}

const res: ResInterface = {
    status: 200,
    statusText: 'okay',
    data: [
        {
            id: '1',
            name: 'ibm',
            place: 'beijing'
        },
        {
            id: '2',
            name: 'google',
            place: 'shanghai'
        }
    ]
}

res.data.forEach(item => {
    console.log(item.id, item.name, item.place)
})*!/

/!*!// type
type GoodsType = {
    id: number,
    name: string,
    price: number
}

// 类型不能同名
/!*type GoodsType = {
    description: string
}*!/

// 类型的继承与接口有区别 交叉类型实现继承
type DiscountGoodsType = GoodsType & {
    disPrice: number
}

const disGoods: DiscountGoodsType = {
    id: 1,
    name: 'ibm',
    price: 100,
    disPrice: 90
}

console.log(disGoods.id, disGoods.name, disGoods.price, disGoods.disPrice)


interface IDepartment {
    id: number,
    name: string
}

interface IDepartment {
    location: string
}

const department:IDepartment = {
    id: 1,
    name: 'ibm',
    location: 'beijing'
}

console.log(department.id, department.name, department.location)*!/

/!*!// 字面量(常量 literal)
const count1: 100 = 100
// count1 = 200
console.log(typeof count1) // number

const s: 'helloworld' = 'helloworld'
console.log(typeof s) // string

// 字面量 + 联合类型  = 枚举
let gender: 'MALE' | 'FEMALE'  = 'MALE'
gender = 'FEMALE'
console.log(gender)*!/


// 声明变量并赋值
// 类型推断
/!*let num = 100
console.log(num)

let num1:number
num1 = 200
console.log(num1)


// 类型推断 通过参数的类型以及函数的实现可以推算出其返回值类型为number，因此返回值类型不用显示给出
const addFun1 = (a: number, b: number) => {
    return a + b;
}

let result1 = addFun1(1, 2);
console.log(result1)*!/

// 任意类型
/!*let count:any
count = 100
count = 'Helloworld'
count = [1,2,3]
count = undefined
console.log(count)*!/

// let linkObj = document.getElementById('link');
//
// // 如果linkObj是null,那么就不会引用href属性，如果不为null则引用
// console.log(linkObj?.href)
//
// // 类型断言
// console.log((linkObj as HTMLAnchorElement).href);

// Java中的泛型 真 or 假 假泛型

type ResType<T> = {
    code: number,
    msg: string,
    data: T
}

type UserType = {
    name: string,
    age: number
}

type GoodsType = {
    id: number,
    goodsName: string
}

const userRes: ResType<UserType> = {
    code: 200,
    msg: 'okay',
    data: {
        name: 'ibm',
        age: 100
    }
}

const goodsRes: ResType<GoodsType> = {
    code: 200,
    msg: 'okay',
    data: {
        id: 1001,
        goodsName: '衬衫'
    }
}

console.log(userRes.data.name, userRes.data.age)
console.log(goodsRes.data.id, goodsRes.data.goodsName)

function fn<T>(a: number, t: T) {
    // ? 类型断言为什么不报错
    return a + (t as string)
}

// let result111 = fn<String>(100, 'aaaaa');
let result222 = fn<number>(100, 2000);
console.log(result222)

*!/

// interface vs type
// extends      交叉类型实现继承
/!*interface Goods {
    id: number,
    name: string,
    price: number
}

// 继承的实现使用extends
interface DiscountGoods extends Goods {
    discountPrice: number
}

const apple: DiscountGoods = {
    id: 1,
    name: 'apple',
    price: 100,
    discountPrice: 90
}

console.log(apple)*!/

/!*type GoodsType = {
    id: number,
    name: string,
    price: number
}

// 类型的继承实现使用交叉类型（&符号表示）
type DiscountGoodsType = GoodsType & {
    discountPrice: number
}

const apple: DiscountGoodsType = {
    id: 1,
    name: 'apple',
    price: 100,
    discountPrice: 90
}

console.log(apple)*!/

// 接口可以重名。属性合并
// 类型不可以重名

// 泛型
// 使用场景：后端发送的数据，我们需要使用一个对象ResType来接收。
// ResType中有公共的成员，但是也有不同的对象。此时我们就可以使用泛型来定义变化的东西
/!*type ResType<T> = {
    code: number,
    msg: string,
    data: T
}

type GoodsType = {
    id: number,
    name: string,
    price: number
}

type UserType = {
    id: number,
    name: string,
    age: number
}

const goodsRes: ResType<GoodsType> = {
    code: 200,
    msg: 'okay',
    data: {
        id: 1001,
        name: '衬衫',
        price: 100
    }
}

const userRes: ResType<UserType> = {
    code: 200,
    msg: 'okay',
    data: {
        id: 1,
        name: 'ibm',
        age: 100
    }
}

console.log(goodsRes.data.id, goodsRes.data.name, goodsRes.data.price)
console.log(userRes.data.id, userRes.data.name, userRes.data.age)*!/

function addFun<T extends number>(a: number, b: T) {
    /!*if (typeof b === 'number') {

        // as:类型断言
        return a + (b as number)
    } else {
        throw new Error('您出错了')
        // return a + 0
    }*!/

    return a + b
}

function addFun1<T>(a: number, b: T) {
    // typeof x 获取x这个变量的数据类型

    /!*const typeResult = typeof b
    console.log(typeResult) // 'number'*!/

    if (typeof b === 'number') {

        // as:类型断言
        return a + (b as number)
    } else {
        throw new Error('您出错了')
        // return a + 0
    }
}

let result = addFun(100, 200);
let result1 = addFun1(100, '200');
console.log(result)
console.log(result1)

*/

// 使用泛型定义一个函数（createArray）
/*function createArray<T>(length: number, value: T) {
    const arr: T[] = []

    for (let i = 0; i < length; i++) {
        arr[i] = value
    }

    return arr
}

const arr = createArray(5, 'TS')
arr.forEach(item => {
    console.log(item)
})*/


// 父类型
type LengthType = {
    length: number
}

// 定义了一个子类型
type ChildLengthType = LengthType & {
    childLength: number
}

/**
 * 如何去引用子类型中的属性
 * @param t
 */
function fn<T extends LengthType>(t: T) {
    console.log(t.length)
    // console.log(t.childLength)
}

const myLength: LengthType = {
    length: 1000
}
fn(myLength)

const myChildLength: ChildLengthType = {
    length: 1000,
    childLength: 2000
}
fn(myChildLength)




















