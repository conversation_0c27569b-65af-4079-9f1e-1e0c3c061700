<script setup>
// TODO: 编辑
import {ref} from 'vue'
import axios from "axios";
// 弹框开关
const dialogVisible = ref(false)
const userForm = ref({})


// 显示编辑弹窗，同时回显数据
const showEditDialog = (row) => {
  dialogVisible.value = true
  userForm.value = row
}

// 将子组件的变量或函数暴露出去。让父组件可以引用
// 父组件需要获取到子组件（模版引用）才能访问
defineExpose({
  showEditDialog
})

let emits = defineEmits(['get-list']);

const handleEdit = () => {

  // 真正地更新
  axios.patch(`/edit/${userForm.value.id}`, {
    name: userForm.value.name,
    place: userForm.value.place,
  })

  // 关闭弹窗
  dialogVisible.value = false

  emits('get-list')
}


</script>

<template>
  <el-dialog v-model="dialogVisible" title="编辑" width="400px">
    <el-form label-width="50px">
      <el-form-item label="姓名">
        <el-input placeholder="请输入姓名" v-model="userForm.name"/>
      </el-form-item>
      <el-form-item label="籍贯">
        <el-input placeholder="请输入籍贯" v-model="userForm.place"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleEdit()">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
.el-input {
  width: 290px;
}
</style>
