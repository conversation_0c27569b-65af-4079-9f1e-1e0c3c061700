package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.service.ElasticsearchQueryService;
import com.hnguigu.springboot.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;

@SpringBootTest
class UserServiceImplTest {

    @Autowired
    private UserService userService;

    @Autowired
    private ElasticsearchQueryService elasticsearchQueryService;

    @Test
    void addUser() {
        User user = new User();
        user.setId(5L);
        user.setName("张飞");
        user.setAge(77);
        user.setAddress("长沙市岳麓区");
        user.setDescription("张飞是一个好人");
        this.userService.addUser(user);
    }

    @Test
    void testDeleteUser() {
        User user = this.userService.findUserById(1L);
        this.userService.deleteUser(user);
    }

    @Test
    void testUpdateUser() {
        User user = this.userService.findUserById(2L);
        user.setName("王五");
        user.setAge(55);
        this.userService.updateUser(user);
    }

    @Test
    void testFindAll() {
        this.userService.findAllUsers().stream().forEach(System.out::println);
    }

    @Test
    void testFindPage() {
        Page<User> page = this.userService.findPage(2, 2);
        System.out.println(page);
    }

    @Test
    public void testFindByNameAndAddress() {
        this.userService.findUserListByNameAndAddress("张飞", "长沙").stream().forEach(System.out::println);
    }

    @Test
    public void testFindByNameOrAddress() {
        System.out.println("=== 测试OR查询 ===");
        this.userService.findUserListByNameOrAddress("张飞", "北京").stream().forEach(System.out::println);
    }

    @Test
    public void testFindByAgeRange() {
        System.out.println("=== 测试年龄范围查询 ===");
        this.userService.findUserListByAgeRange(20, 60).stream().forEach(System.out::println);
    }

    @Test
    public void testFindByNameAndAgeRange() {
        System.out.println("=== 测试姓名和年龄范围组合查询 ===");
        this.userService.findUserListByNameAndAgeRange("张", 30, 80).stream().forEach(System.out::println);
    }

    @Test
    public void testComplexConditions() {
        System.out.println("=== 测试复杂条件查询 ===");
        this.userService.findUserListByComplexConditions("张", "长沙", 20).stream().forEach(System.out::println);
    }

    @Test
    public void testMultiFieldSearch() {
        System.out.println("=== 测试多字段搜索 ===");
        this.elasticsearchQueryService.multiFieldSearch("张飞").stream().forEach(System.out::println);
    }

    @Test
    public void testComplexBoolQuery() {
        System.out.println("=== 测试复杂布尔查询 ===");
        this.elasticsearchQueryService.complexBoolQuery("张", "长沙", 20, 80, "坏人").stream().forEach(System.out::println);
    }

    @Test
    public void testSearchWithSortAndPagination() {
        System.out.println("=== 测试排序和分页查询 ===");
        this.elasticsearchQueryService.searchWithSortAndPagination("张", 0, 5, "age", false).stream().forEach(System.out::println);
    }

    @Test
    public void testPrefixAndWildcardSearch() {
        System.out.println("=== 测试前缀和通配符查询 ===");
        this.elasticsearchQueryService.prefixAndWildcardSearch("张", "*长沙*").stream().forEach(System.out::println);
    }

    @Test
    public void testMultiRangeQuery() {
        System.out.println("=== 测试多范围查询 ===");
        this.elasticsearchQueryService.multiRangeQuery(20, 80, 1L, 10L).stream().forEach(System.out::println);
    }

    @Test
    public void testFindByAgeAndAddress() {
        System.out.println("=== 测试年龄和地址组合查询 ===");
        this.userService.findUserListByAgeAndAddress(20, "长沙").stream().forEach(System.out::println);
    }
}
