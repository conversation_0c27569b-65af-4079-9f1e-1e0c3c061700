package com.hnguigu.springboot.es;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;

@SpringBootTest
public class ElasticsearchRestTemplateTest {

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    /**
     * 建立索引
     */
    @Test
    public void testCreateIndex() {
        boolean flag = this.elasticsearchRestTemplate
                            .indexOps(IndexCoordinates.of("person"))
                            .create();
        if (flag) {
            System.out.println("索引创建成功");
        } else {
            System.out.println("索引创建失败");
        }
    }

    /**
     * 建立索引
     */
    @Test
    public void testDeleteIndex() {
        boolean flag = this.elasticsearchRestTemplate
                .indexOps(IndexCoordinates.of("person"))
                .delete();
        if (flag) {
            System.out.println("索引删除成功");
        } else {
            System.out.println("索引删除失败");
        }
    }
}
