package com.hnguigu.springboot.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor

// indexName="user" 代表索引名称
// shards=3 代表分片数量
// replicas=2 代表副本数量
@Document(indexName = "user", shards = 3, replicas = 2)
public class User implements Serializable {

    private static final long serialVersionUID = 1155196104550632493L;

    /**
     * 用户ID
     */
    @Id
    @Field(type = FieldType.Long, name = "id", store = false, index = false)
    private Long id;

    /**
     * Text vs Keyword
     * Text类型支持分词，Keyword不支持分词
     */
    @Field(type = FieldType.Text, name = "name", store = true, index = true, analyzer = "ik_max_word",
            searchAnalyzer = "ik_smart")
    private String name;

    @Field(type = FieldType.Integer, name = "age", store = false, index = true)
    private Integer age;

    @Field(type = FieldType.Text, name = "address", store = false, index = true, analyzer = "ik_max_word",
            searchAnalyzer = "ik_smart")
    private String address;

    @Field(type = FieldType.Text, name = "description", store = false, index = false, analyzer = "ik_max_word",
            searchAnalyzer = "ik_smart")
    private String description;
}
