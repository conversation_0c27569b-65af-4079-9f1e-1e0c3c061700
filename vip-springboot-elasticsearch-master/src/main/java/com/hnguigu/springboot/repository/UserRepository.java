package com.hnguigu.springboot.repository;

import com.hnguigu.springboot.entity.User;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

import java.util.List;

public interface UserRepository extends ElasticsearchRepository<User, String> {

    User getById(Long id);

    void removeById(Long id);

    /**
     * 命名查询
     * @param name
     * @param address
     * @return
     */
    List<User> findByNameAndAddress(String name, String address);

    List<User> findByAgeAndAddress(Integer age, String address);
}
