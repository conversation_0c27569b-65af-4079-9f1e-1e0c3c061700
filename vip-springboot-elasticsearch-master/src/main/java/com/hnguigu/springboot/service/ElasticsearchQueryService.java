package com.hnguigu.springboot.service;

import com.hnguigu.springboot.entity.User;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Elasticsearch高级查询示例服务
 * 展示各种复杂查询场景的实现方式
 */
@Service
public class ElasticsearchQueryService {

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    /**
     * 示例1：多字段匹配查询（跨字段搜索）
     * 在姓名、地址、描述中搜索关键字
     */
    public List<User> multiFieldSearch(String keyword) {
        if (ObjectUtils.isEmpty(keyword)) {
            throw new RuntimeException("搜索关键字不能为空");
        }

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        // 多字段查询
        queryBuilder.withQuery(
            QueryBuilders.multiMatchQuery(keyword, "name", "address", "description")
                .fuzziness("AUTO") // 支持模糊匹配
        );

        return executeQuery(queryBuilder.build());
    }

    /**
     * 示例2：复杂布尔查询
     * 必须满足：姓名包含关键字
     * 应该满足：地址在指定城市 OR 年龄在指定范围
     * 不能满足：描述包含某些词
     */
    public List<User> complexBoolQuery(String nameKeyword, String city, Integer minAge, Integer maxAge, String excludeKeyword) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // MUST: 必须满足的条件
        if (!ObjectUtils.isEmpty(nameKeyword)) {
            boolQuery.must(QueryBuilders.matchQuery("name", nameKeyword));
        }

        // SHOULD: 应该满足的条件（OR逻辑）
        if (!ObjectUtils.isEmpty(city)) {
            boolQuery.should(QueryBuilders.matchQuery("address", city));
        }

        if (!ObjectUtils.isEmpty(minAge) || !ObjectUtils.isEmpty(maxAge)) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("age");
            if (!ObjectUtils.isEmpty(minAge)) {
                rangeQuery.gte(minAge);
            }
            if (!ObjectUtils.isEmpty(maxAge)) {
                rangeQuery.lte(maxAge);
            }
            boolQuery.should(rangeQuery);
        }

        // 设置should条件的最小匹配数
        if (!ObjectUtils.isEmpty(city) || !ObjectUtils.isEmpty(minAge) || !ObjectUtils.isEmpty(maxAge)) {
            boolQuery.minimumShouldMatch(1);
        }

        // MUST_NOT: 不能满足的条件
        if (!ObjectUtils.isEmpty(excludeKeyword)) {
            boolQuery.mustNot(QueryBuilders.matchQuery("description", excludeKeyword));
        }

        queryBuilder.withQuery(boolQuery);

        return executeQuery(queryBuilder.build());
    }

    /**
     * 示例3：带排序和分页的查询
     */
    public List<User> searchWithSortAndPagination(String keyword, int page, int size, String sortField, boolean ascending) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        // 查询条件
        if (!ObjectUtils.isEmpty(keyword)) {
            queryBuilder.withQuery(
                QueryBuilders.multiMatchQuery(keyword, "name", "address")
            );
        } else {
            queryBuilder.withQuery(QueryBuilders.matchAllQuery());
        }

        // 分页
        queryBuilder.withPageable(PageRequest.of(page, size));

        // 排序
        if (!ObjectUtils.isEmpty(sortField)) {
            SortOrder sortOrder = ascending ? SortOrder.ASC : SortOrder.DESC;
            queryBuilder.withSort(SortBuilders.fieldSort(sortField).order(sortOrder));
        }

        return executeQuery(queryBuilder.build());
    }

    /**
     * 示例4：前缀查询和通配符查询
     */
    public List<User> prefixAndWildcardSearch(String namePrefix, String addressPattern) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 前缀查询
        if (!ObjectUtils.isEmpty(namePrefix)) {
            boolQuery.must(QueryBuilders.prefixQuery("name", namePrefix));
        }

        // 通配符查询
        if (!ObjectUtils.isEmpty(addressPattern)) {
            boolQuery.must(QueryBuilders.wildcardQuery("address", addressPattern));
        }

        queryBuilder.withQuery(boolQuery);

        return executeQuery(queryBuilder.build());
    }

    /**
     * 示例5：范围查询组合
     * 查询指定年龄范围内，且ID在指定范围内的用户
     */
    public List<User> multiRangeQuery(Integer minAge, Integer maxAge, Long minId, Long maxId) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 年龄范围
        if (!ObjectUtils.isEmpty(minAge) || !ObjectUtils.isEmpty(maxAge)) {
            RangeQueryBuilder ageRange = QueryBuilders.rangeQuery("age");
            if (!ObjectUtils.isEmpty(minAge)) {
                ageRange.gte(minAge);
            }
            if (!ObjectUtils.isEmpty(maxAge)) {
                ageRange.lte(maxAge);
            }
            boolQuery.must(ageRange);
        }

        // ID范围
        if (!ObjectUtils.isEmpty(minId) || !ObjectUtils.isEmpty(maxId)) {
            RangeQueryBuilder idRange = QueryBuilders.rangeQuery("id");
            if (!ObjectUtils.isEmpty(minId)) {
                idRange.gte(minId);
            }
            if (!ObjectUtils.isEmpty(maxId)) {
                idRange.lte(maxId);
            }
            boolQuery.must(idRange);
        }

        queryBuilder.withQuery(boolQuery);

        return executeQuery(queryBuilder.build());
    }

    /**
     * 示例6：存在性查询
     * 查询某个字段存在或不存在的文档
     */
    public List<User> existsQuery(String field, boolean shouldExist) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        if (shouldExist) {
            queryBuilder.withQuery(QueryBuilders.existsQuery(field));
        } else {
            queryBuilder.withQuery(
                QueryBuilders.boolQuery()
                    .mustNot(QueryBuilders.existsQuery(field))
            );
        }

        return executeQuery(queryBuilder.build());
    }

    /**
     * 执行查询的通用方法
     */
    private List<User> executeQuery(NativeSearchQuery query) {
        List<User> userList = new ArrayList<>();

        SearchHits<User> searchHits = elasticsearchRestTemplate.search(query, User.class);

        if (!ObjectUtils.isEmpty(searchHits)) {
            List<SearchHit<User>> searchHitList = searchHits.getSearchHits();
            for (SearchHit<User> searchHit : searchHitList) {
                User user = searchHit.getContent();
                userList.add(user);
            }
        }

        return userList;
    }
}
