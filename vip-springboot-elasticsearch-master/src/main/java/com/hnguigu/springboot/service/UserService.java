package com.hnguigu.springboot.service;

import com.hnguigu.springboot.entity.User;
import org.springframework.data.domain.Page;

import java.util.List;

public interface UserService {

    void addUser(User user);

    void deleteUserById(Long id);

    void deleteUser(User user);

    void updateUser(User user);

    User findUserById(Long id);

    List<User> findAllUsers();


    Page<User> findPage(Integer pageNum, Integer pageSize);

    /**
     * 根据姓名和地址查询用户列表
     *
     * @param name
     * @param address
     * @return
     */
    public List<User> findUserListByNameAndAddress(String name, String address);

    /**
     * 根据姓名或地址查询用户列表（OR查询）
     *
     * @param name
     * @param address
     * @return
     */
    public List<User> findUserListByNameOrAddress(String name, String address);

    /**
     * 根据年龄范围查询用户列表
     *
     * @param minAge
     * @param maxAge
     * @return
     */
    public List<User> findUserListByAgeRange(Integer minAge, Integer maxAge);

    /**
     * 复合条件查询：姓名匹配且年龄在指定范围内
     *
     * @param name
     * @param minAge
     * @param maxAge
     * @return
     */
    public List<User> findUserListByNameAndAgeRange(String name, Integer minAge, Integer maxAge);

    /**
     * 复合条件查询：姓名模糊匹配且地址包含关键字且年龄大于指定值
     *
     * @param nameKeyword
     * @param addressKeyword
     * @param minAge
     * @return
     */
    public List<User> findUserListByComplexConditions(String nameKeyword, String addressKeyword, Integer minAge);

    List<User> findUserListByAgeAndAddress(Integer age, String address);

    /**
     * 根据姓名分页查询用户列表
     *
     * @param name
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<User> findPageByName(String name, Integer pageNum, Integer pageSize);
}
