package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.entity.User;
import com.hnguigu.springboot.repository.UserRepository;
import com.hnguigu.springboot.service.UserService;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    // 模板对象
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Override
    public void addUser(User user) {
        if (ObjectUtils.isEmpty(user)) {
            throw new RuntimeException("用户信息不能为空");
        }

        userRepository.save(user);
    }

    @Override
    public void deleteUserById(Long id) {
        if (ObjectUtils.isEmpty(id) || id <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }

        /*User user = this.userRepository.getById(id);
        if (!ObjectUtils.isEmpty(user)) {
            this.userRepository.delete(user);
        }*/

        this.userRepository.removeById(id);
    }

    @Override
    public void deleteUser(User user) {
        if (ObjectUtils.isEmpty(user)) {
            throw new RuntimeException("用户信息不能为空");
        }

        this.userRepository.delete(user);
    }

    @Override
    public void updateUser(User user) {
        if (ObjectUtils.isEmpty(user)) {
            throw new RuntimeException("用户信息不能为空");
        }

        this.userRepository.save(user);
    }

    @Override
    public User findUserById(Long id) {
        if (ObjectUtils.isEmpty(id) || id <= 0) {
            throw new RuntimeException("用户ID不能为空");
        }

        return this.userRepository.getById(id);
    }

    @Override
    public List<User> findAllUsers() {
        List<User> userList = new ArrayList<>();
        Iterator<User> iterator = this.userRepository.findAll().iterator();
        CollectionUtils.addAll(userList, iterator);
        return userList;

        /*PageImpl<User> page = (PageImpl<User>) this.userRepository.findAll();
        return page.getContent();*/
    }

    @Override
    public Page<User> findPage(Integer pageNum, Integer pageSize) {
        if (ObjectUtils.isEmpty(pageNum) || pageNum <= 0) {
            throw new RuntimeException("页码不能为空");
        }

        if (ObjectUtils.isEmpty(pageSize) || pageSize <= 0) {
            throw new RuntimeException("每页显示数量不能为空");
        }

        PageRequest pageRequest = PageRequest.of(pageNum - 1, pageSize);
        return this.userRepository.findAll(pageRequest);
    }

    @Override
    public List<User> findUserListByNameAndAddress(String name, String address) {
        if (ObjectUtils.isEmpty(name)) {
            throw new RuntimeException("姓名不能为空");
        }

        if (ObjectUtils.isEmpty(address)) {
            throw new RuntimeException("地址不能为空");
        }


        return this.userRepository.findByNameAndAddress(name, address);
    }


    /*@Override
    public List<User> findUserListByNameAndAddress(String name, String address) {
        List<User> userList = new ArrayList<>();

        if (ObjectUtils.isEmpty(name)) {
            throw new RuntimeException("姓名不能为空");
        }

        if (ObjectUtils.isEmpty(address)) {
            throw new RuntimeException("地址不能为空");
        }

        // 通过ElasticsearchRestTemplate对象来实现多条件组合查询
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();

        // 使用BoolQuery实现多条件组合查询
        // must: 必须匹配（AND逻辑）
        nativeSearchQueryBuilder.withQuery(QueryBuilders.boolQuery()   // 布尔查询
                .must(QueryBuilders.matchQuery("name", name))  // must相当于and  should相当于or
                .must(QueryBuilders.matchQuery("address", address)));

        NativeSearchQuery nativeSearchQuery = nativeSearchQueryBuilder.build();

        // 执行查询
        SearchHits<User> searchHits = this.elasticsearchRestTemplate.search(nativeSearchQuery, User.class);

        if (!ObjectUtils.isEmpty(searchHits)) {
            List<SearchHit<User>> searchHitList = searchHits.getSearchHits();
            for (SearchHit<User> searchHit : searchHitList) {
                User user = searchHit.getContent();
                userList.add(user);
            }
        }

        return userList;
    }*/

    @Override
    public List<User> findUserListByNameOrAddress(String name, String address) {
        List<User> userList = new ArrayList<>();

        if (ObjectUtils.isEmpty(name) && ObjectUtils.isEmpty(address)) {
            throw new RuntimeException("姓名和地址不能同时为空");
        }

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();

        // 使用BoolQuery的should实现OR查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        if (!ObjectUtils.isEmpty(name)) {
            boolQuery.should(QueryBuilders.matchQuery("name", name));
        }

        if (!ObjectUtils.isEmpty(address)) {
            boolQuery.should(QueryBuilders.matchQuery("address", address));
        }

        // 设置至少匹配一个条件
        boolQuery.minimumShouldMatch(1);

        nativeSearchQueryBuilder.withQuery(boolQuery);
        NativeSearchQuery nativeSearchQuery = nativeSearchQueryBuilder.build();

        SearchHits<User> searchHits = this.elasticsearchRestTemplate.search(nativeSearchQuery, User.class);

        if (!ObjectUtils.isEmpty(searchHits)) {
            List<SearchHit<User>> searchHitList = searchHits.getSearchHits();
            for (SearchHit<User> searchHit : searchHitList) {
                User user = searchHit.getContent();
                userList.add(user);
            }
        }

        return userList;
    }

    /**
     * 根据年龄范围查询
     *
     * @param minAge
     * @param maxAge
     * @return
     */
    @Override
    public List<User> findUserListByAgeRange(Integer minAge, Integer maxAge) {
        List<User> userList = new ArrayList<>();

        if (ObjectUtils.isEmpty(minAge) && ObjectUtils.isEmpty(maxAge)) {
            throw new RuntimeException("年龄范围不能为空");
        }

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();

        // 使用RangeQuery实现范围查询
        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("age");

        if (!ObjectUtils.isEmpty(minAge)) {
            rangeQuery.gte(minAge); // 大于等于
        }

        if (!ObjectUtils.isEmpty(maxAge)) {
            rangeQuery.lte(maxAge); // 小于等于
        }

        nativeSearchQueryBuilder.withQuery(rangeQuery);
        NativeSearchQuery nativeSearchQuery = nativeSearchQueryBuilder.build();

        SearchHits<User> searchHits = this.elasticsearchRestTemplate.search(nativeSearchQuery, User.class);

        if (!ObjectUtils.isEmpty(searchHits)) {
            List<SearchHit<User>> searchHitList = searchHits.getSearchHits();
            for (SearchHit<User> searchHit : searchHitList) {
                User user = searchHit.getContent();
                userList.add(user);
            }
        }

        return userList;
    }

    @Override
    public List<User> findUserListByNameAndAgeRange(String name, Integer minAge, Integer maxAge) {
        List<User> userList = new ArrayList<>();

        if (ObjectUtils.isEmpty(name)) {
            throw new RuntimeException("姓名不能为空");
        }

        if (ObjectUtils.isEmpty(minAge) && ObjectUtils.isEmpty(maxAge)) {
            throw new RuntimeException("年龄范围不能为空");
        }

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();

        // 组合查询：姓名匹配 AND 年龄范围
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 添加姓名匹配条件
        boolQuery.must(QueryBuilders.matchQuery("name", name));

        // 添加年龄范围条件
        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("age");
        if (!ObjectUtils.isEmpty(minAge)) {
            rangeQuery.gte(minAge);
        }
        if (!ObjectUtils.isEmpty(maxAge)) {
            rangeQuery.lte(maxAge);
        }
        boolQuery.must(rangeQuery);

        nativeSearchQueryBuilder.withQuery(boolQuery);
        NativeSearchQuery nativeSearchQuery = nativeSearchQueryBuilder.build();

        SearchHits<User> searchHits = this.elasticsearchRestTemplate.search(nativeSearchQuery, User.class);

        if (!ObjectUtils.isEmpty(searchHits)) {
            List<SearchHit<User>> searchHitList = searchHits.getSearchHits();
            for (SearchHit<User> searchHit : searchHitList) {
                User user = searchHit.getContent();
                userList.add(user);
            }
        }

        return userList;
    }

    @Override
    public List<User> findUserListByComplexConditions(String nameKeyword, String addressKeyword, Integer minAge) {
        List<User> userList = new ArrayList<>();

        if (ObjectUtils.isEmpty(nameKeyword) && ObjectUtils.isEmpty(addressKeyword) && ObjectUtils.isEmpty(minAge)) {
            throw new RuntimeException("查询条件不能全部为空");
        }

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();

        // 复杂组合查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 姓名模糊匹配（如果提供）
        if (!ObjectUtils.isEmpty(nameKeyword)) {
            boolQuery.must(QueryBuilders.wildcardQuery("name", "*" + nameKeyword + "*"));
        }

        // 地址包含关键字（如果提供）
        if (!ObjectUtils.isEmpty(addressKeyword)) {
            boolQuery.must(QueryBuilders.matchQuery("address", addressKeyword));
        }

        // 年龄大于指定值（如果提供）
        if (!ObjectUtils.isEmpty(minAge)) {
            boolQuery.must(QueryBuilders.rangeQuery("age").gte(minAge));
        }

        nativeSearchQueryBuilder.withQuery(boolQuery);
        NativeSearchQuery nativeSearchQuery = nativeSearchQueryBuilder.build();

        SearchHits<User> searchHits = this.elasticsearchRestTemplate.search(nativeSearchQuery, User.class);

        if (!ObjectUtils.isEmpty(searchHits)) {
            List<SearchHit<User>> searchHitList = searchHits.getSearchHits();
            for (SearchHit<User> searchHit : searchHitList) {
                User user = searchHit.getContent();
                userList.add(user);
            }
        }

        return userList;
    }

    @Override
    public List<User> findUserListByAgeAndAddress(Integer age, String address) {
        if (ObjectUtils.isEmpty(age) && ObjectUtils.isEmpty(address)) {
            throw new RuntimeException("年龄和地址不能同时为空");
        }

        return this.userRepository.findByAgeAndAddress(age, address);
    }

    @Override
    public Page<User> findPageByName(String name, Integer pageNum, Integer pageSize) {
        Page<User> page = null;
        List<User> userList = new ArrayList<>();
        if (ObjectUtils.isEmpty(name)) {
            throw new RuntimeException("姓名不能为空");
        }

        if (ObjectUtils.isEmpty(pageNum) || pageSize <= 0) {
            throw new RuntimeException("页码和每页显示数量不能为空");
        }

        if (ObjectUtils.isEmpty(pageSize) || pageSize <= 0) {
            throw new RuntimeException("每页显示数量不能为空");
        }

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(QueryBuilders.matchQuery("name", name)).withPageable(PageRequest.of(pageNum - 1, pageSize)).withSort(SortBuilders.fieldSort("age").order(SortOrder.ASC));
        NativeSearchQuery searchQuery = nativeSearchQueryBuilder.build();
        SearchHits<User> searchHits = this.elasticsearchRestTemplate.search(searchQuery, User.class);
        if (!ObjectUtils.isEmpty(searchHits)) {
            List<SearchHit<User>> searchHitList = searchHits.getSearchHits();
            for (SearchHit<User> searchHit : searchHitList) {
                User user = searchHit.getContent();
                userList.add(user);
            }
        }

        page = new PageImpl<>(userList, PageRequest.of(pageNum - 1, pageSize), searchHits.getTotalHits());
        return page;
    }
}
