# ElasticsearchRestTemplate多条件组合查询指南

## 概述

本指南详细介绍如何使用Spring Data Elasticsearch的`ElasticsearchRestTemplate`实现各种多条件组合查询。

## 基础配置

### 1. 依赖配置
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
</dependency>
```

### 2. 配置文件
```yaml
spring:
  elasticsearch:
    uris: http://localhost:9200
```

### 3. 注入ElasticsearchRestTemplate
```java
@Autowired
private ElasticsearchRestTemplate elasticsearchRestTemplate;
```

## 核心查询构建器

### NativeSearchQueryBuilder
这是构建复杂查询的主要工具：
```java
NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
```

## 多条件组合查询类型

### 1. AND查询（must）
所有条件都必须满足：
```java
BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
boolQuery.must(QueryBuilders.matchQuery("name", "张飞"));
boolQuery.must(QueryBuilders.matchQuery("address", "长沙"));
```

### 2. OR查询（should）
至少满足一个条件：
```java
BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
boolQuery.should(QueryBuilders.matchQuery("name", "张飞"));
boolQuery.should(QueryBuilders.matchQuery("address", "北京"));
boolQuery.minimumShouldMatch(1); // 至少匹配一个
```

### 3. NOT查询（mustNot）
不能满足的条件：
```java
BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
boolQuery.mustNot(QueryBuilders.matchQuery("description", "坏人"));
```

### 4. 范围查询（range）
```java
RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("age");
rangeQuery.gte(20); // 大于等于
rangeQuery.lte(60); // 小于等于
```

## 实际应用示例

### 示例1：基本AND查询
```java
public List<User> findByNameAndAddress(String name, String address) {
    NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
    
    queryBuilder.withQuery(
        QueryBuilders.boolQuery()
            .must(QueryBuilders.matchQuery("name", name))
            .must(QueryBuilders.matchQuery("address", address))
    );
    
    SearchHits<User> searchHits = elasticsearchRestTemplate.search(
        queryBuilder.build(), User.class
    );
    
    return extractUsers(searchHits);
}
```

### 示例2：OR查询
```java
public List<User> findByNameOrAddress(String name, String address) {
    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
    
    if (!ObjectUtils.isEmpty(name)) {
        boolQuery.should(QueryBuilders.matchQuery("name", name));
    }
    
    if (!ObjectUtils.isEmpty(address)) {
        boolQuery.should(QueryBuilders.matchQuery("address", address));
    }
    
    boolQuery.minimumShouldMatch(1);
    
    NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
    queryBuilder.withQuery(boolQuery);
    
    return executeQuery(queryBuilder.build());
}
```

### 示例3：复杂组合查询
```java
public List<User> complexQuery(String nameKeyword, String city, Integer minAge, String excludeKeyword) {
    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
    
    // MUST: 必须满足
    if (!ObjectUtils.isEmpty(nameKeyword)) {
        boolQuery.must(QueryBuilders.matchQuery("name", nameKeyword));
    }
    
    // SHOULD: 应该满足
    if (!ObjectUtils.isEmpty(city)) {
        boolQuery.should(QueryBuilders.matchQuery("address", city));
    }
    
    if (!ObjectUtils.isEmpty(minAge)) {
        boolQuery.should(QueryBuilders.rangeQuery("age").gte(minAge));
    }
    
    // 设置should条件的最小匹配数
    if (!ObjectUtils.isEmpty(city) || !ObjectUtils.isEmpty(minAge)) {
        boolQuery.minimumShouldMatch(1);
    }
    
    // MUST_NOT: 不能满足
    if (!ObjectUtils.isEmpty(excludeKeyword)) {
        boolQuery.mustNot(QueryBuilders.matchQuery("description", excludeKeyword));
    }
    
    NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
    queryBuilder.withQuery(boolQuery);
    
    return executeQuery(queryBuilder.build());
}
```

## 高级查询技巧

### 1. 多字段查询
```java
QueryBuilders.multiMatchQuery(keyword, "name", "address", "description")
    .fuzziness("AUTO") // 支持模糊匹配
```

### 2. 通配符查询
```java
QueryBuilders.wildcardQuery("name", "*张*") // 包含"张"的姓名
```

### 3. 前缀查询
```java
QueryBuilders.prefixQuery("name", "张") // 以"张"开头的姓名
```

### 4. 存在性查询
```java
QueryBuilders.existsQuery("description") // description字段存在
```

### 5. 排序和分页
```java
queryBuilder.withPageable(PageRequest.of(page, size));
queryBuilder.withSort(SortBuilders.fieldSort("age").order(SortOrder.DESC));
```

## 查询执行和结果处理

### 通用执行方法
```java
private List<User> executeQuery(NativeSearchQuery query) {
    List<User> userList = new ArrayList<>();
    
    SearchHits<User> searchHits = elasticsearchRestTemplate.search(query, User.class);
    
    if (!ObjectUtils.isEmpty(searchHits)) {
        List<SearchHit<User>> searchHitList = searchHits.getSearchHits();
        for (SearchHit<User> searchHit : searchHitList) {
            User user = searchHit.getContent();
            userList.add(user);
        }
    }
    
    return userList;
}
```

## 最佳实践

1. **参数验证**：始终验证输入参数
2. **空值处理**：合理处理空值和null值
3. **异常处理**：提供清晰的错误信息
4. **性能优化**：合理使用分页和排序
5. **查询复用**：将通用查询逻辑抽取为公共方法

## 常见查询场景

1. **精确匹配**：使用`termQuery`
2. **模糊匹配**：使用`matchQuery`或`wildcardQuery`
3. **范围查询**：使用`rangeQuery`
4. **多条件AND**：使用`boolQuery().must()`
5. **多条件OR**：使用`boolQuery().should()`
6. **排除条件**：使用`boolQuery().mustNot()`

## 测试建议

运行测试用例验证查询功能：
```bash
mvn test -Dtest=UserServiceImplTest
```

这样可以确保所有查询方法都能正常工作。
