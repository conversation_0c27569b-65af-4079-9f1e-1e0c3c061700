package com.hnguigu.springcloud.service.impl;


import com.hnguigu.springcloud.entity.Goods;
import com.hnguigu.springcloud.entity.Order;
import com.hnguigu.springcloud.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.List;

@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private DiscoveryClient discoveryClient;

    @Override
    public void createOrder(Order order, Long goodsId) {
        if (ObjectUtils.isEmpty(order)) {
            throw new IllegalArgumentException("");
        }

        if (ObjectUtils.isEmpty(goodsId) || goodsId <= 0) {
            throw new IllegalArgumentException("商品id必须大于0");
        }

        // 服务的调用
        /*String url = "http://localhost:7777/api/goods/" + goodsId;
        Goods goods = this.restTemplate.getForObject(url, Goods.class);
        if (!ObjectUtils.isEmpty(goods)) {
            order.setGoods(goods);
        }*/

        // 根据微服务名称获取服务列表
        /*List<ServiceInstance> instanceList = this.discoveryClient.getInstances("goods-service");
        if (!CollectionUtils.isEmpty(instanceList)) {

            // TODO 自己实现负载均衡算法

            ServiceInstance serviceInstance = instanceList.get(0);
            URI uri = serviceInstance.getUri();
            int port = serviceInstance.getPort();

            String url = "http://" + uri.getHost() + ":" + port + "/api/goods/" + goodsId;
            Goods goods = this.restTemplate.getForObject(url, Goods.class);
            if (!ObjectUtils.isEmpty(goods)) {
                order.setGoods(goods);
            }
        }*/


        //
        Goods goods = this.restTemplate.getForObject("http://goods-service/api/goods/" + goodsId, Goods.class);
        if (!ObjectUtils.isEmpty(goods)) {
            order.setGoods(goods);
        }

        log.info("创建订单:{}", order);

    }
}
