package com.hnguigu.springcloud.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class Order implements Serializable {


    private static final long serialVersionUID = 2252451984326498736L;
    private Long id;
    private String name;
    private Date createTime;

    private Goods goods;


}
