server:
  port: 9998

spring:
  application:
    name: eureka-server

eureka:
  client:
    service-url:
      defaultZone: http://${eureka.instance.hostname}:9999/eureka/     # Eureka Server注册中心的地址。微服务连接到Eureka Server的地址
    register-with-eureka: true      # 对于Eureka Server来说不用注册
    fetch-registry: true            # 对于Eureka Server来说不用从注册中心获取服务列表
  instance:
    prefer-ip-address: true          # 指定是否优先使用ip来标识微服务
    hostname: 127.0.0.1
