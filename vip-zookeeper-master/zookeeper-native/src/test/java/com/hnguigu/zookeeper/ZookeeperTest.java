package com.hnguigu.zookeeper;

import org.apache.zookeeper.*;
import org.apache.zookeeper.data.Stat;
import org.junit.Before;
import org.junit.Test;

import java.util.concurrent.CountDownLatch;

public class ZookeeperTest {

    private ZooKeeper zookeeper;

    public static final String CONNECT_STRING = "192.168.27.128:2181";
    public static final int SESSION_TIMEOUT = 20000;

    // 门栓
    private static CountDownLatch countDownLatch = new CountDownLatch(1);

    @Before
    public void setUp() {
        /*try {
            this.zookeeper = new ZooKeeper(CONNECT_STRING, SESSION_TIMEOUT, null);
        } catch (Exception e) {
            e.printStackTrace();
        }*/

        try {
            this.zookeeper = new ZooKeeper(CONNECT_STRING, SESSION_TIMEOUT, new Watcher() {
                @Override
                public void process(WatchedEvent event) {

                    // 事件类型： None(-1),
                    //            NodeCreated(1),
                    //            NodeDeleted(2),
                    //            NodeDataChanged(3),
                    //            NodeChildrenChanged(4),
                    //            DataWatchRemoved(5),
                    //            ChildWatchRemoved(6),
                    //            PersistentWatchRemoved (7);
                    Event.EventType eventType = event.getType();

                    // Zookeeper的状态。SyncConnected，Disconnected...
                    Event.KeeperState state = event.getState();

                    /*if (state == Event.KeeperState.SyncConnected) {
                        System.out.println("连接成功");

                        // 初始化的工作
                        System.out.println("初始化的工作开始执行了");

                        // 打开门栓,发送了一个信号。通知主线程继续向下执行
                        countDownLatch.countDown();
                    }*/

                    if (state == Event.KeeperState.SyncConnected) {
                        if (eventType == Event.EventType.NodeDataChanged) {
                            System.out.println("执行新的业务逻辑");
                        }
                        // 打开门栓,发送了一个信号。通知主线程继续向下执行
                        countDownLatch.countDown();
                    }
                }
            });

            countDownLatch.await();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCreate() {
        try {
            String path = "/abc";
            String s = "helloworld";
            byte[] bytes = s.getBytes("UTF-8");
            String actualPath = this.zookeeper.create(path, bytes, ZooDefs.Ids.OPEN_ACL_UNSAFE,
                    CreateMode.PERSISTENT);
            System.out.println(actualPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGet() {
        try {
            byte[] data = this.zookeeper.getData("/hnguigu", false, null);
            System.out.println(new String(data, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testDelete() {
        try {
            Stat stat = this.zookeeper.exists("/hnguigu", false);
            if (stat != null) {
                if (stat.getNumChildren() == 0) {
                    this.zookeeper.delete("/hnguigu", -1);
                } else {
                    throw new Exception("目录不为空");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSet() {
        try {
            String path = "/hnguigu";
            String newValue = "abc";
            byte[] data = newValue.getBytes("UTF-8");
            Stat stat = this.zookeeper.setData(path, data, -1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试Watcher
     */
    @Test
    public void testWatcher() {
        try {
            // 1.获取/services节点的值，同时监听了/services节点
            byte[] data = this.zookeeper.getData("/services", true, null);
            String result = new String(data, "UTF-8");
            System.out.println(result);

            this.zookeeper.setData("/services", "aaaaaa".getBytes("UTF-8"), -1);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
