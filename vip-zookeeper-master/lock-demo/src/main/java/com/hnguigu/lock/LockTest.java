package com.hnguigu.lock;

public class LockTest {

    public static void main(String[] args) {
        MyThread myThread = new MyThread();

        new Thread(myThread, "卖票点1").start();
        new Thread(myThread, "卖票点2").start();
        new Thread(myThread, "卖票点3").start();
    }
}

/**
 * 卖票过程
 */
class MyThread implements Runnable {

    // 票数 公共资源
    private Integer ticketCount = 10;

    @Override
    public void run() {
        for (int i = 0; i < 30; i++) {
            sale();
        }
    }

    private synchronized void sale() {
        try {
            if (this.ticketCount <= 0) {
                return;
            }
            Thread.sleep(1000);
            System.out.println(Thread.currentThread().getName() + "正在卖第" + (ticketCount--) + "张票");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
