# Java NIO 网络编程演示

这是一个完整的Java NIO网络编程案例，展示了如何使用多路复用技术（Selector）实现高性能的网络服务器和客户端。

## 项目特性

- **多路复用技术**: 使用Java NIO的Selector实现单线程处理多个客户端连接
- **异步I/O**: 非阻塞的读写操作，提高系统吞吐量
- **完整的服务器实现**: 支持客户端连接、消息处理和响应
- **灵活的消息处理**: 可插拔的消息处理器接口
- **多客户端测试**: 提供并发测试工具验证服务器性能
- **交互式客户端**: 支持命令行交互的客户端程序

## 项目结构

```
nio-demo/
├── src/main/java/com/example/nio/
│   ├── server/
│   │   ├── NIOServer.java           # NIO服务器核心实现
│   │   ├── MessageHandler.java      # 消息处理器接口
│   │   └── EchoMessageHandler.java  # Echo消息处理器实现
│   ├── client/
│   │   └── NIOClient.java           # NIO客户端实现
│   └── demo/
│       ├── NIOServerDemo.java       # 服务器演示程序
│       ├── NIOClientDemo.java       # 客户端演示程序
│       └── MultiClientTest.java     # 多客户端测试程序
├── pom.xml                          # Maven配置文件
└── README.md                        # 项目说明文档
```

## 核心技术点

### 1. Selector多路复用
- 使用单个线程管理多个网络连接
- 通过SelectionKey监听不同的I/O事件（ACCEPT、READ、WRITE）
- 事件驱动的编程模型

### 2. 非阻塞I/O
- SocketChannel和ServerSocketChannel配置为非阻塞模式
- 避免线程阻塞，提高并发处理能力

### 3. 缓冲区管理
- 使用ByteBuffer进行数据读写
- 合理的缓冲区大小设置
- 写缓冲区队列管理

## 快速开始

### 编译项目

```bash
cd nio-demo
mvn clean compile
```

### 运行服务器

```bash
# 使用默认端口8080
mvn exec:java@server

# 或者指定端口
mvn exec:java@server -Dexec.args="9090"
```

### 运行客户端

```bash
# 连接到默认服务器localhost:8080
mvn exec:java@client

# 或者指定服务器地址和端口
mvn exec:java@client -Dexec.args="localhost 9090"
```

### 运行多客户端测试

```bash
# 使用默认参数：5个客户端，每个发送3条消息
mvn exec:java@multi-client-test

# 自定义参数：10个客户端，每个发送5条消息
mvn exec:java@multi-client-test -Dexec.args="10 5 localhost 8080"
```

## 使用示例

### 1. 启动服务器

```bash
mvn exec:java@server
```

服务器启动后会显示：
```
NIO服务器启动，监听端口: 8080

=== NIO服务器控制台 ===
可用命令:
  status  - 查看服务器状态
  clients - 查看连接的客户端数量
  help    - 显示帮助信息
  quit    - 停止服务器并退出
========================

服务器控制台>
```

### 2. 启动客户端

```bash
mvn exec:java@client
```

客户端连接后会显示：
```
成功连接到服务器: localhost:8080

=== NIO客户端控制台 ===
连接信息: 连接到: localhost/127.0.0.1:8080
可用服务器命令:
  time    - 获取服务器时间
  ping    - 测试连接
  help    - 显示服务器帮助
  quit    - 断开连接并退出
或者直接输入任何文本进行Echo测试
========================

客户端>
```

### 3. 测试交互

在客户端输入以下命令：

```
客户端> hello world
消息已发送: hello world
收到服务器响应: Echo: hello world - 2024-01-15 10:30:45

客户端> time
消息已发送: time
收到服务器响应: 当前服务器时间: 2024-01-15 10:30:50

客户端> ping
消息已发送: ping
收到服务器响应: pong - 2024-01-15 10:30:55
```

### 4. 多客户端压力测试

```bash
mvn exec:java@multi-client-test -Dexec.args="10 5"
```

测试结果示例：
```
=== NIO多客户端测试 ===
目标服务器: localhost:8080
客户端数量: 10
每客户端消息数: 5
=====================

客户端 1 已连接
客户端 2 已连接
...
客户端 1 消息 1 成功 (耗时: 15ms)
...

=== 测试结果统计 ===
总耗时: 2500ms
客户端总数: 10
成功连接的客户端: 10/10
消息成功率: 50/50 (100%)
平均消息响应时间: 12ms
平均连接时间: 8ms
==================
```

## 支持的服务器命令

客户端可以发送以下特殊命令：

- `time` - 获取服务器当前时间
- `ping` - 测试连接（服务器返回pong）
- `help` - 显示服务器支持的命令
- `quit` 或 `exit` - 请求断开连接
- 其他任何文本 - Echo回显测试

## 性能特点

1. **高并发**: 单线程处理多个客户端连接，避免线程切换开销
2. **低延迟**: 非阻塞I/O减少等待时间
3. **内存效率**: 合理的缓冲区管理，避免内存浪费
4. **可扩展**: 支持自定义消息处理器，易于扩展业务逻辑

## 扩展开发

### 自定义消息处理器

实现`MessageHandler`接口：

```java
public class CustomMessageHandler implements MessageHandler {
    @Override
    public String handleMessage(String message) {
        // 自定义消息处理逻辑
        return "处理结果: " + message;
    }
}
```

### 添加新的客户端命令

在`EchoMessageHandler`中添加新的命令处理：

```java
case "newcommand":
    return "新命令的响应";
```

## 注意事项

1. 确保服务器端口未被占用
2. 防火墙可能需要开放相应端口
3. 大量并发连接时注意系统资源限制
4. 生产环境建议添加日志记录和错误处理

## 技术要求

- Java 8 或更高版本
- Maven 3.6 或更高版本

## 许可证

本项目仅用于学习和演示目的。
