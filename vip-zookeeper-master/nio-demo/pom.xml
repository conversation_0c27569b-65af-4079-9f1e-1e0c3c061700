<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.example</groupId>
    <artifactId>nio-demo</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>NIO Network Programming Demo</name>
    <description>Java NIO网络编程演示项目，使用多路复用技术实现高性能网络通信</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <junit.version>4.13.2</junit.version>
    </properties>

    <dependencies>
        <!-- JUnit for testing -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!-- Maven Exec Plugin for running demos -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <!-- Server Demo -->
                    <execution>
                        <id>server</id>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.example.nio.demo.NIOServerDemo</mainClass>
                            <args>
                                <arg>8080</arg>
                            </args>
                        </configuration>
                    </execution>
                    
                    <!-- Client Demo -->
                    <execution>
                        <id>client</id>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.example.nio.demo.NIOClientDemo</mainClass>
                            <args>
                                <arg>localhost</arg>
                                <arg>8080</arg>
                            </args>
                        </configuration>
                    </execution>
                    
                    <!-- Multi-Client Test -->
                    <execution>
                        <id>multi-client-test</id>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.example.nio.demo.MultiClientTest</mainClass>
                            <args>
                                <arg>5</arg>  <!-- client count -->
                                <arg>3</arg>  <!-- message count per client -->
                                <arg>localhost</arg>
                                <arg>8080</arg>
                            </args>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven JAR Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <mainClass>com.example.nio.demo.NIOServerDemo</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
