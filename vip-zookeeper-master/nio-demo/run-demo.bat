@echo off
chcp 65001 > nul
echo ================================
echo Java NIO 网络编程演示
echo ================================
echo.

:menu
echo 请选择要运行的演示:
echo 1. 启动NIO服务器
echo 2. 启动NIO客户端
echo 3. 运行多客户端测试
echo 4. 运行单元测试
echo 5. 编译项目
echo 6. 清理项目
echo 0. 退出
echo.
set /p choice=请输入选择 (0-6): 

if "%choice%"=="1" goto server
if "%choice%"=="2" goto client
if "%choice%"=="3" goto multitest
if "%choice%"=="4" goto test
if "%choice%"=="5" goto compile
if "%choice%"=="6" goto clean
if "%choice%"=="0" goto exit
echo 无效选择，请重新输入
goto menu

:server
echo.
echo 启动NIO服务器...
echo 默认端口: 8080
echo 按 Ctrl+C 停止服务器
echo.
mvn exec:java@server
goto menu

:client
echo.
echo 启动NIO客户端...
echo 连接到: localhost:8080
echo.
mvn exec:java@client
goto menu

:multitest
echo.
set /p clients=请输入客户端数量 (默认5): 
set /p messages=请输入每客户端消息数 (默认3): 
if "%clients%"=="" set clients=5
if "%messages%"=="" set messages=3
echo.
echo 运行多客户端测试...
echo 客户端数量: %clients%
echo 每客户端消息数: %messages%
echo.
mvn exec:java@multi-client-test -Dexec.args="%clients% %messages%"
pause
goto menu

:test
echo.
echo 运行单元测试...
echo.
mvn test
pause
goto menu

:compile
echo.
echo 编译项目...
echo.
mvn clean compile
pause
goto menu

:clean
echo.
echo 清理项目...
echo.
mvn clean
pause
goto menu

:exit
echo 再见！
exit /b 0
