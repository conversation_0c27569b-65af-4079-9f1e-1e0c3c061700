package com.example.nio.integration;

import com.example.nio.client.NIOClient;
import com.example.nio.server.EchoMessageHandler;
import com.example.nio.server.NIOServer;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * NIO服务器和客户端集成测试
 */
public class NIOIntegrationTest {
    
    private static final String HOST = "localhost";
    private static final int PORT = 18080; // 使用不同端口避免冲突
    
    private NIOServer server;
    private Thread serverThread;
    private volatile boolean serverStarted = false;
    
    @Before
    public void setUp() throws Exception {
        // 启动服务器
        server = new NIOServer(PORT, new EchoMessageHandler());
        
        CountDownLatch serverLatch = new CountDownLatch(1);
        
        serverThread = new Thread(() -> {
            try {
                serverLatch.countDown();
                server.start();
            } catch (IOException e) {
                System.err.println("服务器启动失败: " + e.getMessage());
            }
        });
        
        serverThread.setDaemon(true);
        serverThread.start();
        
        // 等待服务器启动
        serverLatch.await(5, TimeUnit.SECONDS);
        Thread.sleep(1000); // 额外等待确保服务器完全启动
        serverStarted = true;
    }
    
    @After
    public void tearDown() throws Exception {
        if (server != null) {
            server.stop();
        }
        if (serverThread != null) {
            serverThread.interrupt();
        }
    }
    
    @Test
    public void testSingleClientConnection() throws IOException {
        assertTrue("服务器应该已启动", serverStarted);
        
        NIOClient client = new NIOClient(HOST, PORT);
        
        try {
            // 测试连接
            assertTrue("客户端应该能够连接到服务器", client.connect());
            assertTrue("客户端应该处于连接状态", client.isConnected());
            
            // 测试Echo消息
            String testMessage = "Hello NIO Server";
            String response = client.sendAndReceive(testMessage);
            
            assertNotNull("服务器应该返回响应", response);
            assertTrue("响应应该包含原始消息", response.contains(testMessage));
            
        } finally {
            client.disconnect();
        }
    }
    
    @Test
    public void testMultipleMessages() throws IOException {
        assertTrue("服务器应该已启动", serverStarted);
        
        NIOClient client = new NIOClient(HOST, PORT);
        
        try {
            assertTrue("客户端应该能够连接到服务器", client.connect());
            
            // 发送多条消息
            for (int i = 1; i <= 5; i++) {
                String message = "测试消息 " + i;
                String response = client.sendAndReceive(message);
                
                assertNotNull("第" + i + "条消息应该有响应", response);
                assertTrue("响应应该包含消息内容", response.contains(message));
            }
            
        } finally {
            client.disconnect();
        }
    }
    
    @Test
    public void testSpecialCommands() throws IOException {
        assertTrue("服务器应该已启动", serverStarted);
        
        NIOClient client = new NIOClient(HOST, PORT);
        
        try {
            assertTrue("客户端应该能够连接到服务器", client.connect());
            
            // 测试time命令
            String timeResponse = client.sendAndReceive("time");
            assertNotNull("time命令应该有响应", timeResponse);
            assertTrue("time响应应该包含时间信息", timeResponse.contains("当前服务器时间"));
            
            // 测试ping命令
            String pingResponse = client.sendAndReceive("ping");
            assertNotNull("ping命令应该有响应", pingResponse);
            assertTrue("ping响应应该包含pong", pingResponse.contains("pong"));
            
            // 测试help命令
            String helpResponse = client.sendAndReceive("help");
            assertNotNull("help命令应该有响应", helpResponse);
            assertTrue("help响应应该包含命令信息", helpResponse.contains("可用命令"));
            
        } finally {
            client.disconnect();
        }
    }
    
    @Test
    public void testMultipleClients() throws Exception {
        assertTrue("服务器应该已启动", serverStarted);
        
        final int clientCount = 3;
        final CountDownLatch latch = new CountDownLatch(clientCount);
        final boolean[] results = new boolean[clientCount];
        
        // 启动多个客户端
        for (int i = 0; i < clientCount; i++) {
            final int clientId = i;
            
            Thread clientThread = new Thread(() -> {
                NIOClient client = new NIOClient(HOST, PORT);
                try {
                    if (client.connect()) {
                        String message = "客户端" + clientId + "的消息";
                        String response = client.sendAndReceive(message);
                        
                        results[clientId] = response != null && response.contains(message);
                    }
                } catch (IOException e) {
                    System.err.println("客户端" + clientId + "发生错误: " + e.getMessage());
                } finally {
                    try {
                        client.disconnect();
                    } catch (IOException e) {
                        // 忽略断开连接时的错误
                    }
                    latch.countDown();
                }
            });
            
            clientThread.start();
        }
        
        // 等待所有客户端完成
        assertTrue("所有客户端应该在10秒内完成", latch.await(10, TimeUnit.SECONDS));
        
        // 检查结果
        for (int i = 0; i < clientCount; i++) {
            assertTrue("客户端" + i + "应该成功完成通信", results[i]);
        }
    }
    
    @Test
    public void testClientReconnection() throws IOException, InterruptedException {
        assertTrue("服务器应该已启动", serverStarted);
        
        NIOClient client = new NIOClient(HOST, PORT);
        
        try {
            // 第一次连接
            assertTrue("第一次连接应该成功", client.connect());
            
            String response1 = client.sendAndReceive("第一次连接的消息");
            assertNotNull("第一次消息应该有响应", response1);
            
            // 断开连接
            client.disconnect();
            assertFalse("断开后应该不处于连接状态", client.isConnected());
            
            Thread.sleep(100); // 短暂等待
            
            // 重新连接
            assertTrue("重新连接应该成功", client.connect());
            
            String response2 = client.sendAndReceive("重新连接的消息");
            assertNotNull("重新连接后的消息应该有响应", response2);
            
        } finally {
            client.disconnect();
        }
    }
}
