package com.example.nio.server;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * EchoMessageHandler单元测试
 */
public class EchoMessageHandlerTest {
    
    private EchoMessageHandler handler;
    
    @Before
    public void setUp() {
        handler = new EchoMessageHandler();
    }
    
    @Test
    public void testEchoMessage() {
        String message = "Hello World";
        String response = handler.handleMessage(message);
        
        assertNotNull("响应不应为null", response);
        assertTrue("响应应包含原始消息", response.contains(message));
        assertTrue("响应应包含Echo前缀", response.startsWith("Echo:"));
    }
    
    @Test
    public void testTimeCommand() {
        String response = handler.handleMessage("time");
        
        assertNotNull("时间命令响应不应为null", response);
        assertTrue("响应应包含时间信息", response.contains("当前服务器时间"));
    }
    
    @Test
    public void testPingCommand() {
        String response = handler.handleMessage("ping");
        
        assertNotNull("ping命令响应不应为null", response);
        assertTrue("响应应包含pong", response.contains("pong"));
    }
    
    @Test
    public void testHelpCommand() {
        String response = handler.handleMessage("help");
        
        assertNotNull("help命令响应不应为null", response);
        assertTrue("响应应包含可用命令信息", response.contains("可用命令"));
    }
    
    @Test
    public void testQuitCommand() {
        String response = handler.handleMessage("quit");
        
        assertNotNull("quit命令响应不应为null", response);
        assertTrue("响应应包含再见信息", response.contains("再见"));
    }
    
    @Test
    public void testExitCommand() {
        String response = handler.handleMessage("exit");
        
        assertNotNull("exit命令响应不应为null", response);
        assertTrue("响应应包含再见信息", response.contains("再见"));
    }
    
    @Test
    public void testEmptyMessage() {
        String response = handler.handleMessage("");
        
        assertNotNull("空消息响应不应为null", response);
        assertTrue("响应应包含空消息提示", response.contains("空消息"));
    }
    
    @Test
    public void testNullMessage() {
        String response = handler.handleMessage(null);
        
        assertNotNull("null消息响应不应为null", response);
        assertTrue("响应应包含空消息提示", response.contains("空消息"));
    }
    
    @Test
    public void testCaseInsensitiveCommands() {
        String response1 = handler.handleMessage("TIME");
        String response2 = handler.handleMessage("Time");
        String response3 = handler.handleMessage("time");
        
        assertNotNull("大写TIME命令响应不应为null", response1);
        assertNotNull("混合大小写Time命令响应不应为null", response2);
        assertNotNull("小写time命令响应不应为null", response3);
        
        assertTrue("所有格式的time命令都应返回时间信息", 
                  response1.contains("当前服务器时间") && 
                  response2.contains("当前服务器时间") && 
                  response3.contains("当前服务器时间"));
    }
    
    @Test
    public void testWhitespaceHandling() {
        String response1 = handler.handleMessage("  ping  ");
        String response2 = handler.handleMessage("\ttime\n");
        
        assertNotNull("带空格的ping命令响应不应为null", response1);
        assertNotNull("带制表符和换行的time命令响应不应为null", response2);
        
        assertTrue("带空格的ping命令应正常工作", response1.contains("pong"));
        assertTrue("带制表符的time命令应正常工作", response2.contains("当前服务器时间"));
    }
}
