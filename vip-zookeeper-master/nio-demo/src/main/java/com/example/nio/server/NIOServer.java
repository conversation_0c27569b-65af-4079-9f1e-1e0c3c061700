package com.example.nio.server;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.*;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * NIO服务器实现，使用Selector多路复用技术
 * 支持多客户端连接，异步处理读写操作
 */
public class NIOServer {
    
    private final int port;
    private final MessageHandler messageHandler;
    private ServerSocketChannel serverSocketChannel;
    private Selector selector;
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    // 存储每个客户端的写缓冲区
    private final ConcurrentHashMap<SocketChannel, ByteBuffer> writeBuffers = new ConcurrentHashMap<>();
    
    // 缓冲区大小
    private static final int BUFFER_SIZE = 1024;
    
    public NIOServer(int port, MessageHandler messageHandler) {
        this.port = port;
        this.messageHandler = messageHandler;
    }
    
    /**
     * 启动服务器
     */
    public void start() throws IOException {
        // 创建ServerSocketChannel
        serverSocketChannel = ServerSocketChannel.open();
        serverSocketChannel.configureBlocking(false);
        serverSocketChannel.bind(new InetSocketAddress(port));
        
        // 创建Selector
        selector = Selector.open();
        
        // 注册ServerSocketChannel到Selector，监听ACCEPT事件
        serverSocketChannel.register(selector, SelectionKey.OP_ACCEPT);
        
        running.set(true);
        System.out.println("NIO服务器启动，监听端口: " + port);
        
        // 主事件循环
        eventLoop();
    }
    
    /**
     * 主事件循环
     */
    private void eventLoop() throws IOException {
        while (running.get()) {
            // 阻塞等待事件发生
            int readyChannels = selector.select(1000);
            
            if (readyChannels == 0) {
                continue;
            }
            
            // 获取就绪的SelectionKey集合
            Set<SelectionKey> selectedKeys = selector.selectedKeys();
            Iterator<SelectionKey> keyIterator = selectedKeys.iterator();
            
            while (keyIterator.hasNext()) {
                SelectionKey key = keyIterator.next();
                keyIterator.remove();
                
                try {
                    if (key.isValid()) {
                        handleKey(key);
                    }
                } catch (Exception e) {
                    System.err.println("处理客户端连接时发生错误: " + e.getMessage());
                    closeChannel(key);
                }
            }
        }
    }
    
    /**
     * 处理SelectionKey事件
     */
    private void handleKey(SelectionKey key) throws IOException {
        if (key.isAcceptable()) {
            handleAccept(key);
        } else if (key.isReadable()) {
            handleRead(key);
        } else if (key.isWritable()) {
            handleWrite(key);
        }
    }
    
    /**
     * 处理客户端连接请求
     */
    private void handleAccept(SelectionKey key) throws IOException {
        ServerSocketChannel serverChannel = (ServerSocketChannel) key.channel();
        SocketChannel clientChannel = serverChannel.accept();
        
        if (clientChannel != null) {
            clientChannel.configureBlocking(false);
            
            // 注册客户端通道到Selector，监听READ事件
            clientChannel.register(selector, SelectionKey.OP_READ);
            
            System.out.println("新客户端连接: " + clientChannel.getRemoteAddress());
        }
    }
    
    /**
     * 处理客户端读取事件
     */
    private void handleRead(SelectionKey key) throws IOException {
        SocketChannel clientChannel = (SocketChannel) key.channel();
        ByteBuffer buffer = ByteBuffer.allocate(BUFFER_SIZE);
        
        int bytesRead = clientChannel.read(buffer);
        
        if (bytesRead > 0) {
            buffer.flip();
            byte[] data = new byte[buffer.remaining()];
            buffer.get(data);
            
            String message = new String(data, "UTF-8");
            System.out.println("收到客户端消息: " + message.trim());
            
            // 处理消息并准备响应
            String response = messageHandler.handleMessage(message.trim());
            
            if (response != null && !response.isEmpty()) {
                // 准备写入响应
                ByteBuffer writeBuffer = ByteBuffer.wrap((response + "\n").getBytes("UTF-8"));
                writeBuffers.put(clientChannel, writeBuffer);
                
                // 注册写事件
                key.interestOps(SelectionKey.OP_READ | SelectionKey.OP_WRITE);
            }
        } else if (bytesRead == -1) {
            // 客户端断开连接
            System.out.println("客户端断开连接: " + clientChannel.getRemoteAddress());
            closeChannel(key);
        }
    }
    
    /**
     * 处理客户端写入事件
     */
    private void handleWrite(SelectionKey key) throws IOException {
        SocketChannel clientChannel = (SocketChannel) key.channel();
        ByteBuffer writeBuffer = writeBuffers.get(clientChannel);
        
        if (writeBuffer != null) {
            int bytesWritten = clientChannel.write(writeBuffer);
            
            if (!writeBuffer.hasRemaining()) {
                // 写入完成，移除写缓冲区并取消写事件监听
                writeBuffers.remove(clientChannel);
                key.interestOps(SelectionKey.OP_READ);
                System.out.println("响应发送完成，字节数: " + bytesWritten);
            }
        }
    }
    
    /**
     * 关闭客户端连接
     */
    private void closeChannel(SelectionKey key) {
        try {
            SocketChannel clientChannel = (SocketChannel) key.channel();
            writeBuffers.remove(clientChannel);
            key.cancel();
            clientChannel.close();
        } catch (IOException e) {
            System.err.println("关闭客户端连接时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 停止服务器
     */
    public void stop() throws IOException {
        running.set(false);
        
        if (selector != null) {
            selector.close();
        }
        
        if (serverSocketChannel != null) {
            serverSocketChannel.close();
        }
        
        writeBuffers.clear();
        System.out.println("NIO服务器已停止");
    }
    
    /**
     * 获取当前连接的客户端数量
     */
    public int getConnectedClientCount() {
        return writeBuffers.size();
    }
}
