package com.example.nio.demo;

import com.example.nio.client.NIOClient;

import java.io.IOException;
import java.util.Scanner;

/**
 * NIO客户端演示类
 * 提供交互式客户端界面
 */
public class NIOClientDemo {
    
    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 8080;
    
    public static void main(String[] args) {
        String host = DEFAULT_HOST;
        int port = DEFAULT_PORT;
        
        // 从命令行参数获取主机和端口
        if (args.length >= 1) {
            host = args[0];
        }
        if (args.length >= 2) {
            try {
                port = Integer.parseInt(args[1]);
            } catch (NumberFormatException e) {
                System.err.println("无效的端口号，使用默认端口: " + DEFAULT_PORT);
            }
        }
        
        NIOClient client = new NIOClient(host, port);
        
        try {
            // 连接到服务器
            if (!client.connect()) {
                System.err.println("无法连接到服务器");
                return;
            }
            
            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                try {
                    System.out.println("\n正在断开连接...");
                    client.disconnect();
                } catch (IOException e) {
                    System.err.println("断开连接时发生错误: " + e.getMessage());
                }
            }));
            
            // 处理用户交互
            handleUserInteraction(client);
            
        } catch (IOException e) {
            System.err.println("客户端运行时发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                client.disconnect();
            } catch (IOException e) {
                System.err.println("断开连接时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理用户交互
     */
    private static void handleUserInteraction(NIOClient client) throws IOException {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("\n=== NIO客户端控制台 ===");
        System.out.println("连接信息: " + client.getConnectionInfo());
        System.out.println("可用服务器命令:");
        System.out.println("  time    - 获取服务器时间");
        System.out.println("  ping    - 测试连接");
        System.out.println("  help    - 显示服务器帮助");
        System.out.println("  quit    - 断开连接并退出");
        System.out.println("或者直接输入任何文本进行Echo测试");
        System.out.println("========================\n");
        
        while (client.isConnected()) {
            System.out.print("客户端> ");
            String input = scanner.nextLine();
            
            if (input == null) {
                break;
            }
            
            input = input.trim();
            
            if (input.isEmpty()) {
                continue;
            }
            
            // 处理本地命令
            if (input.equalsIgnoreCase("quit") || input.equalsIgnoreCase("exit")) {
                System.out.println("正在断开连接...");
                break;
            }
            
            if (input.equalsIgnoreCase("status")) {
                System.out.println("连接状态: " + (client.isConnected() ? "已连接" : "未连接"));
                System.out.println("连接信息: " + client.getConnectionInfo());
                continue;
            }
            
            try {
                // 发送消息并接收响应
                String response = client.sendAndReceive(input);
                
                if (response == null) {
                    System.out.println("服务器无响应或连接已断开");
                    break;
                }
                
                // 如果服务器返回退出消息，断开连接
                if (response.toLowerCase().contains("再见")) {
                    System.out.println("服务器请求断开连接");
                    break;
                }
                
            } catch (IOException e) {
                System.err.println("通信错误: " + e.getMessage());
                break;
            }
        }
        
        System.out.println("客户端会话结束");
    }
}
