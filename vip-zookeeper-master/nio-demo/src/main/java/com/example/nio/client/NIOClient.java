package com.example.nio.client;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * NIO客户端实现
 * 支持异步连接、发送和接收消息
 */
public class NIOClient {
    
    private final String host;
    private final int port;
    private SocketChannel socketChannel;
    private final AtomicBoolean connected = new AtomicBoolean(false);
    
    // 缓冲区大小
    private static final int BUFFER_SIZE = 1024;
    
    public NIOClient(String host, int port) {
        this.host = host;
        this.port = port;
    }
    
    /**
     * 连接到服务器
     */
    public boolean connect() throws IOException {
        socketChannel = SocketChannel.open();
        socketChannel.configureBlocking(false);
        
        // 尝试连接
        boolean connected = socketChannel.connect(new InetSocketAddress(host, port));
        
        if (!connected) {
            // 非阻塞连接，需要等待连接完成
            while (!socketChannel.finishConnect()) {
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }
        
        this.connected.set(true);
        System.out.println("成功连接到服务器: " + host + ":" + port);
        return true;
    }
    
    /**
     * 发送消息到服务器
     */
    public boolean sendMessage(String message) throws IOException {
        if (!connected.get() || socketChannel == null) {
            System.err.println("客户端未连接到服务器");
            return false;
        }
        
        ByteBuffer buffer = ByteBuffer.wrap((message + "\n").getBytes("UTF-8"));
        
        while (buffer.hasRemaining()) {
            int bytesWritten = socketChannel.write(buffer);
            if (bytesWritten == 0) {
                // 如果写入0字节，稍等片刻再试
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }
        
        System.out.println("消息已发送: " + message);
        return true;
    }
    
    /**
     * 从服务器接收消息
     */
    public String receiveMessage() throws IOException {
        if (!connected.get() || socketChannel == null) {
            System.err.println("客户端未连接到服务器");
            return null;
        }
        
        ByteBuffer buffer = ByteBuffer.allocate(BUFFER_SIZE);
        StringBuilder messageBuilder = new StringBuilder();
        
        while (true) {
            buffer.clear();
            int bytesRead = socketChannel.read(buffer);
            
            if (bytesRead > 0) {
                buffer.flip();
                byte[] data = new byte[buffer.remaining()];
                buffer.get(data);
                
                String chunk = new String(data, "UTF-8");
                messageBuilder.append(chunk);
                
                // 检查是否收到完整消息（以换行符结尾）
                if (chunk.contains("\n")) {
                    break;
                }
            } else if (bytesRead == 0) {
                // 没有数据可读，稍等片刻
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            } else {
                // 连接已关闭
                System.out.println("服务器关闭了连接");
                connected.set(false);
                break;
            }
        }
        
        String message = messageBuilder.toString().trim();
        if (!message.isEmpty()) {
            System.out.println("收到服务器响应: " + message);
        }
        
        return message.isEmpty() ? null : message;
    }
    
    /**
     * 发送消息并等待响应
     */
    public String sendAndReceive(String message) throws IOException {
        if (sendMessage(message)) {
            return receiveMessage();
        }
        return null;
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return connected.get() && socketChannel != null && socketChannel.isConnected();
    }
    
    /**
     * 断开连接
     */
    public void disconnect() throws IOException {
        connected.set(false);
        
        if (socketChannel != null) {
            socketChannel.close();
            socketChannel = null;
        }
        
        System.out.println("已断开与服务器的连接");
    }
    
    /**
     * 获取连接信息
     */
    public String getConnectionInfo() {
        if (isConnected()) {
            try {
                return "连接到: " + socketChannel.getRemoteAddress();
            } catch (IOException e) {
                return "连接信息获取失败";
            }
        }
        return "未连接";
    }
}
