package com.example.nio.server;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Echo消息处理器实现
 * 将客户端发送的消息原样返回，并添加时间戳
 */
public class EchoMessageHandler implements MessageHandler {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public String handleMessage(String message) {
        if (message == null || message.trim().isEmpty()) {
            return "服务器收到空消息 - " + LocalDateTime.now().format(FORMATTER);
        }
        
        // 处理特殊命令
        String trimmedMessage = message.trim().toLowerCase();
        
        switch (trimmedMessage) {
            case "time":
                return "当前服务器时间: " + LocalDateTime.now().format(FORMATTER);
            case "ping":
                return "pong - " + LocalDateTime.now().format(FORMATTER);
            case "quit":
            case "exit":
                return "再见！连接即将关闭 - " + LocalDateTime.now().format(FORMATTER);
            case "help":
                return "可用命令: time, ping, quit/exit, help - " + LocalDateTime.now().format(FORMATTER);
            default:
                return "Echo: " + message + " - " + LocalDateTime.now().format(FORMATTER);
        }
    }
}
