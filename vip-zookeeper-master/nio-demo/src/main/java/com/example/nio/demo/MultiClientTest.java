package com.example.nio.demo;

import com.example.nio.client.NIOClient;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 多客户端测试类
 * 用于测试NIO服务器的多路复用能力
 */
public class MultiClientTest {
    
    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 8080;
    private static final int DEFAULT_CLIENT_COUNT = 5;
    private static final int DEFAULT_MESSAGE_COUNT = 3;
    
    public static void main(String[] args) {
        String host = DEFAULT_HOST;
        int port = DEFAULT_PORT;
        int clientCount = DEFAULT_CLIENT_COUNT;
        int messageCount = DEFAULT_MESSAGE_COUNT;
        
        // 解析命令行参数
        if (args.length >= 1) {
            try {
                clientCount = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                System.err.println("无效的客户端数量，使用默认值: " + DEFAULT_CLIENT_COUNT);
            }
        }
        
        if (args.length >= 2) {
            try {
                messageCount = Integer.parseInt(args[1]);
            } catch (NumberFormatException e) {
                System.err.println("无效的消息数量，使用默认值: " + DEFAULT_MESSAGE_COUNT);
            }
        }
        
        if (args.length >= 3) {
            host = args[2];
        }
        
        if (args.length >= 4) {
            try {
                port = Integer.parseInt(args[3]);
            } catch (NumberFormatException e) {
                System.err.println("无效的端口号，使用默认端口: " + DEFAULT_PORT);
            }
        }
        
        System.out.println("=== NIO多客户端测试 ===");
        System.out.println("目标服务器: " + host + ":" + port);
        System.out.println("客户端数量: " + clientCount);
        System.out.println("每客户端消息数: " + messageCount);
        System.out.println("=====================\n");
        
        MultiClientTest test = new MultiClientTest();
        test.runTest(host, port, clientCount, messageCount);
    }
    
    /**
     * 运行多客户端测试
     */
    public void runTest(String host, int port, int clientCount, int messageCount) {
        ExecutorService executor = Executors.newFixedThreadPool(clientCount);
        CountDownLatch latch = new CountDownLatch(clientCount);
        List<TestResult> results = new ArrayList<>();
        
        long startTime = System.currentTimeMillis();
        
        // 启动多个客户端
        for (int i = 0; i < clientCount; i++) {
            final int clientId = i + 1;
            executor.submit(() -> {
                TestResult result = runSingleClientTest(host, port, clientId, messageCount);
                synchronized (results) {
                    results.add(result);
                }
                latch.countDown();
            });
        }
        
        try {
            // 等待所有客户端完成
            latch.await(30, TimeUnit.SECONDS);
            
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            
            // 输出测试结果
            printTestResults(results, totalTime);
            
        } catch (InterruptedException e) {
            System.err.println("测试被中断: " + e.getMessage());
        } finally {
            executor.shutdown();
        }
    }
    
    /**
     * 运行单个客户端测试
     */
    private TestResult runSingleClientTest(String host, int port, int clientId, int messageCount) {
        TestResult result = new TestResult(clientId);
        NIOClient client = new NIOClient(host, port);
        
        try {
            long connectStart = System.currentTimeMillis();
            
            // 连接到服务器
            if (!client.connect()) {
                result.setError("连接失败");
                return result;
            }
            
            long connectTime = System.currentTimeMillis() - connectStart;
            result.setConnectTime(connectTime);
            
            System.out.println("客户端 " + clientId + " 已连接");
            
            // 发送消息
            for (int i = 1; i <= messageCount; i++) {
                String message = "客户端" + clientId + "的消息" + i;
                
                long messageStart = System.currentTimeMillis();
                String response = client.sendAndReceive(message);
                long messageTime = System.currentTimeMillis() - messageStart;
                
                if (response != null) {
                    result.addSuccessfulMessage(messageTime);
                    System.out.println("客户端 " + clientId + " 消息 " + i + " 成功 (耗时: " + messageTime + "ms)");
                } else {
                    result.addFailedMessage();
                    System.err.println("客户端 " + clientId + " 消息 " + i + " 失败");
                }
                
                // 短暂延迟
                Thread.sleep(100);
            }
            
            // 发送特殊命令测试
            client.sendAndReceive("time");
            client.sendAndReceive("ping");
            
        } catch (Exception e) {
            result.setError("运行时错误: " + e.getMessage());
            System.err.println("客户端 " + clientId + " 发生错误: " + e.getMessage());
        } finally {
            try {
                client.disconnect();
                System.out.println("客户端 " + clientId + " 已断开连接");
            } catch (IOException e) {
                System.err.println("客户端 " + clientId + " 断开连接时发生错误: " + e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 打印测试结果
     */
    private void printTestResults(List<TestResult> results, long totalTime) {
        System.out.println("\n=== 测试结果统计 ===");
        System.out.println("总耗时: " + totalTime + "ms");
        System.out.println("客户端总数: " + results.size());
        
        int successfulClients = 0;
        int totalMessages = 0;
        int successfulMessages = 0;
        long totalMessageTime = 0;
        long totalConnectTime = 0;
        
        for (TestResult result : results) {
            if (result.getError() == null) {
                successfulClients++;
            }
            
            totalMessages += result.getTotalMessages();
            successfulMessages += result.getSuccessfulMessages();
            totalMessageTime += result.getTotalMessageTime();
            totalConnectTime += result.getConnectTime();
            
            System.out.println("客户端 " + result.getClientId() + ": " + 
                             result.getSuccessfulMessages() + "/" + result.getTotalMessages() + 
                             " 消息成功" + 
                             (result.getError() != null ? " (错误: " + result.getError() + ")" : ""));
        }
        
        System.out.println("\n成功连接的客户端: " + successfulClients + "/" + results.size());
        System.out.println("消息成功率: " + successfulMessages + "/" + totalMessages + 
                          " (" + (totalMessages > 0 ? (successfulMessages * 100 / totalMessages) : 0) + "%)");
        
        if (successfulMessages > 0) {
            System.out.println("平均消息响应时间: " + (totalMessageTime / successfulMessages) + "ms");
        }
        
        if (successfulClients > 0) {
            System.out.println("平均连接时间: " + (totalConnectTime / successfulClients) + "ms");
        }
        
        System.out.println("==================");
    }
    
    /**
     * 测试结果类
     */
    private static class TestResult {
        private final int clientId;
        private long connectTime;
        private int successfulMessages = 0;
        private int totalMessages = 0;
        private long totalMessageTime = 0;
        private String error;
        
        public TestResult(int clientId) {
            this.clientId = clientId;
        }
        
        public void setConnectTime(long connectTime) {
            this.connectTime = connectTime;
        }
        
        public void addSuccessfulMessage(long messageTime) {
            successfulMessages++;
            totalMessages++;
            totalMessageTime += messageTime;
        }
        
        public void addFailedMessage() {
            totalMessages++;
        }
        
        public void setError(String error) {
            this.error = error;
        }
        
        // Getters
        public int getClientId() { return clientId; }
        public long getConnectTime() { return connectTime; }
        public int getSuccessfulMessages() { return successfulMessages; }
        public int getTotalMessages() { return totalMessages; }
        public long getTotalMessageTime() { return totalMessageTime; }
        public String getError() { return error; }
    }
}
