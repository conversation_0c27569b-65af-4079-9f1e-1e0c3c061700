package com.example.nio.demo;

import com.example.nio.server.EchoMessageHandler;
import com.example.nio.server.NIOServer;

import java.io.IOException;
import java.util.Scanner;

/**
 * NIO服务器演示类
 * 启动服务器并提供简单的控制台交互
 */
public class NIOServerDemo {
    
    private static final int DEFAULT_PORT = 8080;
    
    public static void main(String[] args) {
        int port = DEFAULT_PORT;
        
        // 从命令行参数获取端口号
        if (args.length > 0) {
            try {
                port = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                System.err.println("无效的端口号，使用默认端口: " + DEFAULT_PORT);
            }
        }
        
        // 创建消息处理器
        EchoMessageHandler messageHandler = new EchoMessageHandler();
        
        // 创建NIO服务器
        NIOServer server = new NIOServer(port, messageHandler);
        
        // 添加关闭钩子，确保服务器正常关闭
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                System.out.println("\n正在关闭服务器...");
                server.stop();
            } catch (IOException e) {
                System.err.println("关闭服务器时发生错误: " + e.getMessage());
            }
        }));
        
        try {
            // 在新线程中启动服务器
            Thread serverThread = new Thread(() -> {
                try {
                    server.start();
                } catch (IOException e) {
                    System.err.println("服务器启动失败: " + e.getMessage());
                }
            });
            
            serverThread.setDaemon(false);
            serverThread.start();
            
            // 主线程处理控制台输入
            handleConsoleInput(server);
            
        } catch (Exception e) {
            System.err.println("服务器运行时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理控制台输入
     */
    private static void handleConsoleInput(NIOServer server) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("\n=== NIO服务器控制台 ===");
        System.out.println("可用命令:");
        System.out.println("  status  - 查看服务器状态");
        System.out.println("  clients - 查看连接的客户端数量");
        System.out.println("  help    - 显示帮助信息");
        System.out.println("  quit    - 停止服务器并退出");
        System.out.println("========================\n");
        
        while (true) {
            System.out.print("服务器控制台> ");
            String input = scanner.nextLine().trim().toLowerCase();
            
            switch (input) {
                case "status":
                    System.out.println("服务器状态: 运行中");
                    break;
                    
                case "clients":
                    System.out.println("当前连接的客户端数量: " + server.getConnectedClientCount());
                    break;
                    
                case "help":
                    System.out.println("可用命令: status, clients, help, quit");
                    break;
                    
                case "quit":
                case "exit":
                    try {
                        System.out.println("正在停止服务器...");
                        server.stop();
                        System.out.println("服务器已停止，程序退出");
                        System.exit(0);
                    } catch (IOException e) {
                        System.err.println("停止服务器时发生错误: " + e.getMessage());
                    }
                    break;
                    
                case "":
                    // 空输入，忽略
                    break;
                    
                default:
                    System.out.println("未知命令: " + input + "，输入 'help' 查看可用命令");
                    break;
            }
        }
    }
}
