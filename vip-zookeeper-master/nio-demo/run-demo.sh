#!/bin/bash

echo "================================"
echo "Java NIO 网络编程演示"
echo "================================"
echo

show_menu() {
    echo "请选择要运行的演示:"
    echo "1. 启动NIO服务器"
    echo "2. 启动NIO客户端"
    echo "3. 运行多客户端测试"
    echo "4. 运行单元测试"
    echo "5. 编译项目"
    echo "6. 清理项目"
    echo "0. 退出"
    echo
}

while true; do
    show_menu
    read -p "请输入选择 (0-6): " choice
    
    case $choice in
        1)
            echo
            echo "启动NIO服务器..."
            echo "默认端口: 8080"
            echo "按 Ctrl+C 停止服务器"
            echo
            mvn exec:java@server
            ;;
        2)
            echo
            echo "启动NIO客户端..."
            echo "连接到: localhost:8080"
            echo
            mvn exec:java@client
            ;;
        3)
            echo
            read -p "请输入客户端数量 (默认5): " clients
            read -p "请输入每客户端消息数 (默认3): " messages
            clients=${clients:-5}
            messages=${messages:-3}
            echo
            echo "运行多客户端测试..."
            echo "客户端数量: $clients"
            echo "每客户端消息数: $messages"
            echo
            mvn exec:java@multi-client-test -Dexec.args="$clients $messages"
            read -p "按回车键继续..."
            ;;
        4)
            echo
            echo "运行单元测试..."
            echo
            mvn test
            read -p "按回车键继续..."
            ;;
        5)
            echo
            echo "编译项目..."
            echo
            mvn clean compile
            read -p "按回车键继续..."
            ;;
        6)
            echo
            echo "清理项目..."
            echo
            mvn clean
            read -p "按回车键继续..."
            ;;
        0)
            echo "再见！"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
    echo
done
