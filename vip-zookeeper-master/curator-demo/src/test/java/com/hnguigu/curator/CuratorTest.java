package com.hnguigu.curator;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.api.CuratorEvent;
import org.apache.curator.framework.api.CuratorListener;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

public class CuratorTest {

    private CuratorFramework client;
    public static final String CONNECT_STRING = "192.168.27.128:2181";
    public static final int SESSION_TIMEOUT = 20000;
    public static final int CONNECTION_TIMEOUT = 20000;

    @Before
    public void setUp() {
        // Create a retry policy with initial sleep time of 1000ms and max retries of 3
        ExponentialBackoffRetry retryPolicy = new ExponentialBackoffRetry(1000, 3);

        this.client = CuratorFrameworkFactory.newClient(CONNECT_STRING, SESSION_TIMEOUT,
                CONNECTION_TIMEOUT, retryPolicy);

        // Start the client
        // 连接Zookeeper服务器
        client.start();
    }

    @Test
    public void testCreate() {
        // Fluent interface 链式编程
        try {
            this.client.create().forPath("/curator", "helloworld".getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGet() {
        // Fluent interface 链式编程
        try {
            byte[] data = this.client.getData().forPath("/curator");
            System.out.println(new String(data, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 同步更新操作
     */
    @Test
    public void testSet() {
        // Fluent interface 链式编程
        try {
            this.client.setData().forPath("/curator", "abc".getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSetAsync() {
        try {
            CuratorListener listener = new CuratorListener() {
                @Override
                public void eventReceived(CuratorFramework client, CuratorEvent event) throws Exception {
                    // examine event for details
                }
            };
            this.client.getCuratorListenable().addListener(listener);
            this.client.setData()
                    .inBackground()
                    .forPath("/curator", "abc".getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSetAsync1() {
        try {
            /*this.client.setData().inBackground(new BackgroundCallback() {
                @Override
                public void processResult(CuratorFramework client, CuratorEvent event) throws
                Exception {
                    System.out.println("执行新的业务逻辑");
                }
            }).forPath("/curator", "abc".getBytes());*/

            this.client.setData().inBackground((client, event) -> {
                System.out.println(event.getName());
            }).forPath("/curator", "abc".getBytes());


        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void testDelete() {
        // Fluent interface 链式编程
        try {
            this.client.delete().forPath("/curator");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testFindChildren() {
        // Fluent interface 链式编程
        try {
            List<String> childrenList = this.client.getChildren().forPath("/");
            childrenList.stream().forEach(System.out::println);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
