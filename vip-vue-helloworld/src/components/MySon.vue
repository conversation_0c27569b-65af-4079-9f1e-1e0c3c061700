<script setup lang="ts">
import {useShadowRoot} from 'vue';

defineProps({
  message: String,
  count: {
    type: Number,
    default: 10
  },
  user: Object
})
</script>

<template>
  <div>
    <button>点击</button>
    <!--    <h1>{{ message }}</h1>
        <h1>{{ count }}</h1>
        <h1>{{ user.id }}-&#45;&#45;&#45;&#45;{{ user.name }}</h1>-->
  </div>

  <div>
    <button>点击</button>

    <!--    <h1>{{ message }}</h1>
        <h1>{{ count }}</h1>
        <h1>{{ user.id }}-&#45;&#45;&#45;&#45;{{ user.name }}</h1>-->
  </div>

  <div>
    <button>点击</button>
  </div>
</template>

<style scoped>

</style>
