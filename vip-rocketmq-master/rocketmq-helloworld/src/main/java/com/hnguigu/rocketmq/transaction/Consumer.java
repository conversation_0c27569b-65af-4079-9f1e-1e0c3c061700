package com.hnguigu.rocketmq.transaction;

import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.List;

public class Consumer {

    public static final String NAME_SRV_ADDRESS = "**************:9876";
    public static final String CONSUMER_GROUP = "consumer-group";

    public static void main(String[] args) {
        DefaultMQPushConsumer consumer = null;
        try {
            consumer = new DefaultMQPushConsumer();
            consumer.setNamesrvAddr(NAME_SRV_ADDRESS);
            consumer.setConsumerGroup(CONSUMER_GROUP);

            // 从何处开始消费消息。
            // ConsumeFromWhere枚举，有三个成员。
            // CONSUME_FROM_LAST_OFFSET：表示从最后一个偏移量开始消费。
            // CONSUME_FROM_FIRST_OFFSET: 表示从队列的第一个偏移量开始消费。
            // CONSUME_FROM_TIMESTAMP: 表示从指定时间戳开始消费。
            consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);

            // 开始消费消息
            // 订阅主题 第二个参数：表达式（过滤消息）
            consumer.subscribe("TopicTransaction", "*");

            consumer.registerMessageListener(new MessageListenerConcurrently() {
                @Override
                public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs,
                                                                ConsumeConcurrentlyContext context) {
                    if (CollectionUtils.isNotEmpty(msgs)) {
                        for (MessageExt msg : msgs) {
                            String tags = msg.getTags();
                            System.out.println(tags + "------" + new String(msg.getBody()));
                        }
                    }
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
            });


            consumer.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
