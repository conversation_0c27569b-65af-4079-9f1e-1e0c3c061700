package com.hnguigu.rocketmq.transaction;


import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.TransactionMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;

import java.io.UnsupportedEncodingException;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 消息的生产者
 */
public class Producer {

    public static final String NAME_SRV_ADDRESS = "**************:9876";
    public static final String PRODUCER_GROUP = "producer-group";

    public static void main(String[] args) {
        TransactionMQProducer producer = null;
        try {
            producer = new TransactionMQProducer();
            producer.setNamesrvAddr(NAME_SRV_ADDRESS);
            producer.setProducerGroup(PRODUCER_GROUP);
            producer.setTransactionListener(new MyTransactionListener());

            ExecutorService executorService = new ThreadPoolExecutor(2, 5, 100, TimeUnit.SECONDS,
                    new ArrayBlockingQueue<>(2000), r -> {
                Thread thread = new Thread(r);
                thread.setName("client-transaction-msg-check-thread");
                return thread;
            });

            producer.setExecutorService(executorService);

            producer.start();

            String[] tags = new String[]{"TagA", "TagB", "TagC", "TagD", "TagE"};
            for (int i = 0; i < 100; i++) {
                try {
                    Message msg = new Message("TopicTransaction", tags[i % tags.length], "KEY" + i, ("Hello RocketMQ "
                            + i).getBytes(RemotingHelper.DEFAULT_CHARSET));
//                msg.putUserProperty("CHECK_IMMUNITY_TIME_IN_SECONDS","10000");

                    // 发送事务消息
                    SendResult sendResult = producer.sendMessageInTransaction(msg, null);
                    System.out.printf("%s%n", sendResult);

                    Thread.sleep(10);
                } catch (MQClientException | UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }

            for (int i = 0; i < 100000; i++) {
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {

            producer.shutdown();
        }
    }
}
