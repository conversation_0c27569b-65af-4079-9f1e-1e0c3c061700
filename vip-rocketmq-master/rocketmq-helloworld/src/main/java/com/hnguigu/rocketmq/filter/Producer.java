package com.hnguigu.rocketmq.filter;


import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;

/**
 * 消息的生产者
 */
public class Producer {

    public static final String NAME_SRV_ADDRESS = "**************:9876";
    public static final String PRODUCER_GROUP = "producer-group";

    public static void main(String[] args) {
        DefaultMQProducer producer = null;
        try {
            producer = new DefaultMQProducer();
            producer.setNamesrvAddr(NAME_SRV_ADDRESS);
            producer.setProducerGroup(PRODUCER_GROUP);

            producer.start();

            // 创建Message
            Message message1 = new Message("TopicFilter", "TagA", "OrderID001", "Hello world RocketMQ-1".getBytes());
            message1.putUserProperty("a", String.valueOf(10));
            producer.send(message1);

            Message message2 = new Message("TopicFilter", "TagB", "OrderID002", "Hello world RocketMQ-2".getBytes());
            message2.putUserProperty("a", String.valueOf(20));
            producer.send(message2);

            Message message3 = new Message("TopicFilter", "TagC", "OrderID003", "Hello world RocketMQ-3".getBytes());
            message3.putUserProperty("a", String.valueOf(30));
            producer.send(message3);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            producer.shutdown();
        }
    }
}
