package com.hnguigu.rocketmq.order;


import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.rocketmq.remoting.common.RemotingHelper;

import java.util.List;

/**
 * 消息的生产者
 */
public class Producer {

    public static final String NAME_SRV_ADDRESS = "**************:9876";
    public static final String PRODUCER_GROUP = "producer-group";

    public static void main(String[] args) {
        DefaultMQProducer producer = null;
        SendResult result = null;
        try {
            producer = new DefaultMQProducer();
            producer.setNamesrvAddr(NAME_SRV_ADDRESS);
            producer.setProducerGroup(PRODUCER_GROUP);

            producer.start();


            // 模拟10个订单
            for (int i = 0; i < 10; i++) {
                int orderId = i;

                // 每个订单5个步骤。这个5个步骤（创建订单，扣减库存，扣减余额，物流，完成）发送消息
                for (int j = 0; j < 5; j++) {
                    Message msg = new Message("TopicOrder", "order_" + orderId, "KEY" + orderId,
                            ("order_" + orderId + " step " + j).getBytes(RemotingHelper.DEFAULT_CHARSET));

                    result = producer.send(msg, new MessageQueueSelector() {
                        @Override
                        public MessageQueue select(List<MessageQueue> mqs, Message msg, Object arg) {
                            Integer id = (Integer) arg;
                            int index = id % mqs.size();
                            return mqs.get(index);
                        }
                    }, orderId);
                }

            }

            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            producer.shutdown();
        }
    }
}
