package com.hnguigu.rocketmq.broadcast;


import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;

/**
 * 消息的生产者
 */
public class Producer {

    public static final String NAME_SRV_ADDRESS = "**************:9876";
    public static final String PRODUCER_GROUP = "producer-group";

    public static void main(String[] args) {
        DefaultMQProducer producer = null;
        try {
            producer = new DefaultMQProducer();
            producer.setNamesrvAddr(NAME_SRV_ADDRESS);
            producer.setProducerGroup(PRODUCER_GROUP);

            producer.start();

            // 创建Message
            Message message = new Message("TopicBroadcast", "TagA", "OrderID001", "Hello world RocketMQ!".getBytes());

            // 发送消息，返回一个发送结果。发送结果中就有发送确认状态
            SendResult result = producer.send(message);
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            producer.shutdown();
        }
    }
}
