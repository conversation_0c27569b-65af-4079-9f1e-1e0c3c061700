<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>

    <groupId>com.hnguigu</groupId>
    <artifactId>vip-springboot-rabbitmq-master</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>springboot-helloworld-producer</module>
        <module>springboot-helloworld-consumer</module>
        <module>springboot-workqueue-producer</module>
        <module>springboot-workqueue-consumer1</module>
        <module>springboot-workqueue-consumer2</module>
        <module>springboot-rabbitmq-pubsub-producer-7777</module>
        <module>springboot-rabbitmq-pubsub-consumer-6666</module>
        <module>springboot-rabbitmq-route-producer-5555</module>
        <module>springboot-rabbitmq-route-consumer-4444</module>
        <module>springboot-rabbitmq-topic-producer-3333</module>
        <module>springboot-rabbitmq-topic-consumer-2222</module>
        <module>springboot-rabbitmq-confirm-8899</module>
        <module>springboot-rabbitmq-confrim-consumer-7788</module>
        <module>springboot-rabbitmq-ack-consumer-8877</module>
        <module>springboot-rabbitmq-ack-producer-7676</module>
        <module>springboot-rabbitmq-deadletter-producer-6666</module>
        <module>springboot-rabbitmq-deadletter-consumer-5555</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.38</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
