package com.hnguigu.springboot.listener;

import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

@Component
public class SmsListener {

    @RabbitListener(queues = {"smsQueue"})
    public void receiveMessage(Message message, Channel channel) {
        System.out.println("SmsListener收到消息：" + message);
    }
}
