package com.hnguigu.springboot.config;

import org.springframework.amqp.core.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * WorkQueue模式，不需要声明交换机（使用默认交换机）
 */
@Configuration
@ConfigurationProperties(prefix = "queue")
public class RabbitMQConfig {

    private String confirmQueueName;
    private String exchangeName;

    public void setConfirmQueueName(String confirmQueueName) {
        this.confirmQueueName = confirmQueueName;
    }

    public void setExchangeName(String exchangeName) {
        this.exchangeName = exchangeName;
    }

    /**
     * 声明交换机
     */
    @Bean
    public Exchange directExchange() {
        return ExchangeBuilder.directExchange(this.exchangeName).build();
    }

    /**
     * 声明队列
     */
    @Bean
    public Queue confirmQueue() {
        return QueueBuilder.durable(this.confirmQueueName).build();
    }

    /**
     * 绑定队列和交换机
     */
    @Bean
    public Binding bindSmsQueueToExchange(Queue confirmQueue, Exchange directExchange) {
        return BindingBuilder.bind(confirmQueue).to(directExchange).with("confirm").noargs();
    }

}
