package com.hnguigu.springboot.config;

import org.springframework.amqp.core.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;


/**
 * WorkQueue模式，不需要声明交换机（使用默认交换机）
 */
@Configuration
@ConfigurationProperties(prefix = "queue")
public class RabbitMQConfig {

    private String normalQueueName;
    private String deadLetterQueueName;
    private String normalExchangeName;
    private String deadLetterExchangeName;

    public void setNormalQueueName(String normalQueueName) {
        this.normalQueueName = normalQueueName;
    }

    public void setDeadLetterQueueName(String deadLetterQueueName) {
        this.deadLetterQueueName = deadLetterQueueName;
    }

    public void setNormalExchangeName(String normalExchangeName) {
        this.normalExchangeName = normalExchangeName;
    }

    public void setDeadLetterExchangeName(String deadLetterExchangeName) {
        this.deadLetterExchangeName = deadLetterExchangeName;
    }

    /**
     * 声明交换机
     */
    @Bean
    public Exchange normalExchange() {
        return ExchangeBuilder.directExchange(this.normalExchangeName).build();
    }

    @Bean
    public Exchange deadLetterExchange() {
        return ExchangeBuilder.directExchange(this.deadLetterExchangeName).build();
    }

    /**
     * 声明队列
     */
    @Bean
    public Queue normalQueue() {
        Map<String, Object> arguments = new HashMap();
        arguments.put("x-message-ttl", "5000");
        arguments.put("x-dead-letter-exchange", this.deadLetterExchangeName);
        arguments.put("x-dead-letter-routing-key", "deadLetter");

        return QueueBuilder.durable(this.normalQueueName).withArguments(arguments).build();
    }

    @Bean
    public Queue deadLetterQueue() {
        return QueueBuilder.durable(this.deadLetterQueueName).build();
    }


    /**
     * 绑定队列和交换机
     */
    @Bean
    public Binding bindNormalQueueToExchange(Queue normalQueue, Exchange normalExchange) {
        return BindingBuilder.bind(normalQueue).to(normalExchange).with("normal").noargs();
    }

    @Bean
    public Binding bindDeadLetterQueueToExchange(Queue deadLetterQueue,
                                                 Exchange deadLetterExchange) {
        return BindingBuilder.bind(deadLetterQueue).to(deadLetterExchange).with("deadLetter").noargs();
    }

}
