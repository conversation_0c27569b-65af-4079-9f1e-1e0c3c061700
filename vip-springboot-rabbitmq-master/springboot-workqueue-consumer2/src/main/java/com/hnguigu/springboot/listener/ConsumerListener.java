package com.hnguigu.springboot.listener;

import com.rabbitmq.client.*;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class ConsumerListener {

    @RabbitListener(queues = {"workQueue"})
    public void receiveMessage(Message message, Channel channel) {
        try {

            // 设置prefetchCount为1，表示每次只消费一条消息
            // 让消费方一定要消费完（做应答）才让其消费下一条消息
            // 为了实现公平分发
            channel.basicQos(1);

            // TODO 消费消息.手动应答
            // 第二个参数设置为false,意味着是手动应答
            // 第三个参数是一个Consumer接口对象，

            Consumer consumer = new DefaultConsumer(channel) {
                @Override
                public void handleDelivery(String consumerTag, Envelope envelope,
                                           AMQP.BasicProperties properties, byte[] body) throws IOException {
                    // TODO 获取 MessageProperties
                    long deliveryTag = envelope.getDeliveryTag();
                    System.out.println("ConsumerListener收到消息：" + new String(body));
                    channel.basicAck(deliveryTag, false);
                }
            };

            channel.basicConsume("workQueue", false, consumer);

            TimeUnit.MILLISECONDS.sleep(800);

            /*// TODO 获取 MessageProperties
            MessageProperties messageProperties = message.getMessageProperties();
            long deliveryTag = messageProperties.getDeliveryTag();
            channel.basicAck(deliveryTag, false);
            System.out.println("ConsumerListener收到消息：" + message);*/
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
