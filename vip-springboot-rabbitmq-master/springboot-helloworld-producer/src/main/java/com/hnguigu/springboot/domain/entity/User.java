package com.hnguigu.springboot.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class User implements Serializable {


    private static final long serialVersionUID = 870937595790447685L;
    private Long id;
    private String name;
    private String password;
    private Float salary;

    // TODO issue 解决日期转换问题
    // private Date birthday;
}
