package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.domain.entity.User;
import com.hnguigu.springboot.service.ProducerService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Service
public class ProducerServiceImpl implements ProducerService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public void sendMessage(String message) {
        if (StringUtils.isEmpty(message)) {
            throw new RuntimeException("消息不能为空");
        }

        // 向helloworldQueue发送消息
        this.rabbitTemplate.convertAndSend("helloworldQueue", message);
    }

    @Override
    public void sendMessage(User user) {
        if (ObjectUtils.isEmpty(user)) {
            throw new RuntimeException("消息不能为空");
        }

        // 向helloworldQueue发送消息
        this.rabbitTemplate.convertAndSend("helloworldQueue", user);
    }
}
