package com.hnguigu.springboot.config;

import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * Helloworld模式，不需要声明交换机（使用默认交换机）
 */
@Configuration
@ConfigurationProperties(prefix = "queue")
public class RabbitMQConfig {

    private String queueName;

    public void setQueueName(String queueName) {
        this.queueName = queueName;
    }

    /**
     * 声明交换机
     */

    /**
     * 声明队列
     */
    @Bean
    public Queue helloworldQueue() {
        return QueueBuilder.durable(this.queueName).build();
    }

    /**
     * 绑定队列和交换机
     */
}
