package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.service.ProducerService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class ProducerServiceImpl implements ProducerService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public void sendMessage(String message, String routingKey) {
        if (StringUtils.isEmpty(message)) {
            throw new RuntimeException("消息不能为空");
        }

        this.rabbitTemplate.convertAndSend("topicExchange", routingKey, message);
    }
}
