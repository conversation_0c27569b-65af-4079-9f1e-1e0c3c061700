package com.hnguigu.springboot.config;

import org.springframework.amqp.core.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * WorkQueue模式，不需要声明交换机（使用默认交换机）
 */
@Configuration
@ConfigurationProperties(prefix = "queue")
public class RabbitMQConfig {

    private String smsQueueName;
    private String emailQueueName;
    private String exchangeName;

    public void setSmsQueueName(String smsQueueName) {
        this.smsQueueName = smsQueueName;
    }

    public void setEmailQueueName(String emailQueueName) {
        this.emailQueueName = emailQueueName;
    }

    public void setExchangeName(String exchangeName) {
        this.exchangeName = exchangeName;
    }

    /**
     * 声明交换机
     */
    @Bean
    public Exchange fanoutExchange() {
        return ExchangeBuilder.fanoutExchange(this.exchangeName).build();
    }

    /**
     * 声明队列
     */
    @Bean
    public Queue smsQueue() {
        return QueueBuilder.durable(this.smsQueueName).build();
    }

    @Bean
    public Queue emailQueue() {
        return QueueBuilder.durable(this.emailQueueName).build();
    }

    /**
     * 绑定队列和交换机
     */
    @Bean
    public Binding bindSmsQueueToExchange(Queue smsQueue, Exchange fanoutExchange) {
        return BindingBuilder.bind(smsQueue).to(fanoutExchange).with("").noargs();
    }

    @Bean
    public Binding bindEmailQueueToExchange(Queue emailQueue, Exchange fanoutExchange) {
        return BindingBuilder.bind(emailQueue).to(fanoutExchange).with("").noargs();
    }
}
