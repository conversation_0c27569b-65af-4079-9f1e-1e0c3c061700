package com.hnguigu.springboot.listener;

import com.rabbitmq.client.*;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class EmailListener {

    @RabbitListener(queues = {"emailQueue"})
    public void receiveMessage(String message) {
        System.out.println("EmailListener收到消息：" + message);
    }
}
