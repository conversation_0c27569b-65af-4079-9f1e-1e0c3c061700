package com.hnguigu.springboot.listener;

import com.rabbitmq.client.*;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class SmsListener {

    @RabbitListener(queues = {"smsQueue"})
    public void receiveMessage(Message message, Channel channel) {
        System.out.println("SmsListener收到消息：" + message);
    }
}
