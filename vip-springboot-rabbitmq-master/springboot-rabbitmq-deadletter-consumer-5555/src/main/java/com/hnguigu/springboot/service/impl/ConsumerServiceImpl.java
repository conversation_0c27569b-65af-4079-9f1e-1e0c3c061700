package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.service.ConsumerService;
import com.rabbitmq.client.*;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class ConsumerServiceImpl implements ConsumerService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public String receiveMessage() {
        ConnectionFactory connectionFactory = this.rabbitTemplate.getConnectionFactory();

        // 获取到Connection
        Connection connection = connectionFactory.createConnection();

        // 创建通道
        Channel channel = connection.createChannel(false);

        try {
            Consumer consumer = new DefaultConsumer(channel) {
                @Override
                public void handleDelivery(String consumerTag, Envelope envelope,
                                           AMQP.BasicProperties properties, byte[] body) throws IOException {
                    // TODO 获取 MessageProperties
                    long deliveryTag = envelope.getDeliveryTag();
                    System.out.println("ConsumerListener收到消息：" + new String(body));

                    String message = new String(body);
                    try {
                        // TODO 根据消息调用业务逻辑，根据执行的情况（业务执行很正常|业务执行异常）
                        if ("error".equals(message)) {
                            throw new RuntimeException("业务执行异常");
                        }

                        channel.basicAck(deliveryTag, false);
                    } catch (Exception e) {

                        // 第三个参数为true表示重回队列
                        channel.basicNack(deliveryTag, false, true);
                    }
                }
            };

            // 第二个参数为false意味着开启了手动应答
            channel.basicConsume("ackQueue", false, consumer);
        } catch (IOException e) {
            e.printStackTrace();
        }


        return "";
    }
}
