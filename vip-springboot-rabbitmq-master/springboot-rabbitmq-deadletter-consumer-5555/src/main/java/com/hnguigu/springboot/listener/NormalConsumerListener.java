package com.hnguigu.springboot.listener;

import com.rabbitmq.client.*;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class NormalConsumerListener {

    @RabbitListener(queues = {"normalQueue"})
    public void receiveMessage(Message message, Channel channel) {
        try {
            Consumer consumer = new DefaultConsumer(channel) {
                @Override
                public void handleDelivery(String consumerTag, Envelope envelope,
                                           AMQP.BasicProperties properties, byte[] body) throws IOException {
                    // TODO 获取 MessageProperties
                    long deliveryTag = envelope.getDeliveryTag();
                    System.out.println("ConsumerListener收到消息：" + new String(body));

                    String message = new String(body);

                    // 重新入列
                    channel.basicNack(deliveryTag, false, true);
                }
            };

            // 第二个参数为false意味着开启了手动应答
            channel.basicConsume("normalQueue", false, consumer);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
