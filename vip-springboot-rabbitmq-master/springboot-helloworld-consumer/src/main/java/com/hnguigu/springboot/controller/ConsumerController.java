package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.domain.entity.User;
import com.hnguigu.springboot.service.ConsumerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/consumers")
@Slf4j
public class ConsumerController {

    @Autowired
    private ConsumerService consumerService;

    @GetMapping
    public String receive() {
        Object object = this.consumerService.receiveObject();
        log.info("user:{}", object);
        return "success";
    }
}
