package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.domain.entity.User;
import com.hnguigu.springboot.service.ConsumerService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ConsumerServiceImpl implements ConsumerService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public Object receiveObject() {
        Object object = this.rabbitTemplate.receiveAndConvert("helloworldQueue");
        return object;
    }
}
