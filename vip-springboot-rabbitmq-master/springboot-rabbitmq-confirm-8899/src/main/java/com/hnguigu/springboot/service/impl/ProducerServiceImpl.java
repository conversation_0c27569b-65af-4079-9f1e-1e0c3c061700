package com.hnguigu.springboot.service.impl;

import com.hnguigu.springboot.service.ProducerService;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class ProducerServiceImpl implements ProducerService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public void sendMessage(String message, String routingKey) {
        if (StringUtils.isEmpty(message)) {
            throw new RuntimeException("消息不能为空");
        }

        // TODO 设置确认回调
        // 发送方与交换机之间的确认
        this.rabbitTemplate.setConfirmCallback(new RabbitTemplate.ConfirmCallback() {
            @Override
            public void confirm(CorrelationData correlationData, boolean ack, String cause) {
                if (ack) { // ack为true表示消息发送成功
                    System.out.println("消息发送成功");
                } else { // ack为false表示消息发送失败
                    System.out.println("消息发送失败，失败的原因为：" + cause);
                }
            }
        });


        // TODO 设置返回回调
        this.rabbitTemplate.setMandatory(true);
        this.rabbitTemplate.setReturnsCallback(new RabbitTemplate.ReturnsCallback() {
            @Override
            public void returnedMessage(ReturnedMessage returned) {
                // This method is called when a message is lost
                System.out.println("消息丢失，丢失的消息为：" + returned);
                // Print out the lost message
            }
        });

        this.rabbitTemplate.convertAndSend("directExchange", routingKey, message);
    }
}
