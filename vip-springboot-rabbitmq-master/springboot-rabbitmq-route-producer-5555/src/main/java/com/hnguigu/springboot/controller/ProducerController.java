package com.hnguigu.springboot.controller;

import com.hnguigu.springboot.service.ProducerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/producers")
public class ProducerController {

    @Autowired
    private ProducerService producerService;

    @PostMapping
    public String send(String message, String routingKey) {
        this.producerService.sendMessage(message, routingKey);
        return "ok";
    }
}
