package com.hnguigu.springboot.config;

import org.springframework.amqp.core.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * WorkQueue模式，不需要声明交换机（使用默认交换机）
 */
@Configuration
@ConfigurationProperties(prefix = "queue")
public class RabbitMQConfig {

    private String ackQueueName;
    private String exchangeName;

    public void setAckQueueName(String ackQueueName) {
        this.ackQueueName = ackQueueName;
    }

    public void setExchangeName(String exchangeName) {
        this.exchangeName = exchangeName;
    }

    /**
     * 声明交换机
     */
    @Bean
    public Exchange directExchange() {
        return ExchangeBuilder.directExchange(this.exchangeName).build();
    }

    /**
     * 声明队列
     */
    @Bean
    public Queue ackQueue() {
        return QueueBuilder.durable(this.ackQueueName).build();
    }

    /**
     * 绑定队列和交换机
     */
    @Bean
    public Binding bindSmsQueueToExchange(Queue ackQueue, Exchange directExchange) {
        return BindingBuilder.bind(ackQueue).to(directExchange).with("ack").noargs();
    }

}
