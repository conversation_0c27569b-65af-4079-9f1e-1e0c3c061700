package com.hnguigu.springboot.listener;

import com.rabbitmq.client.*;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

//@Component
public class ConfirmListener {

//    @RabbitListener(queues = {"ackQueue"})
    public void receiveMessage(Channel channel) {
        try {
            Consumer consumer = new DefaultConsumer(channel) {
                @Override
                public void handleDelivery(String consumerTag, Envelope envelope,
                                           AMQP.BasicProperties properties, byte[] body) throws IOException {
                    // TODO 获取 MessageProperties
                    long deliveryTag = envelope.getDeliveryTag();
                    System.out.println("ConsumerListener收到消息：" + new String(body));

                    String message = new String(body);
                    try {
                        // TODO 根据消息调用业务逻辑，根据执行的情况（业务执行很正常|业务执行异常）
                        if ("error".equals(message)) {
                            throw new RuntimeException("业务执行异常");
                        }

                        channel.basicAck(deliveryTag, false);
                    } catch (Exception e) {

                        // 第三个参数为true表示重回队列
                        channel.basicNack(deliveryTag, false, true);
                    }
                }
            };

            // 第二个参数为false意味着开启了手动应答
            channel.basicConsume("ackQueue", false, consumer);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
